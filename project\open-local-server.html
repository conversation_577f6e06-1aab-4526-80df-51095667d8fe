<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فتح السيرفر المحلي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            max-width: 600px;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .server-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 20px 40px;
            margin: 15px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: transform 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        
        .server-button:hover {
            transform: translateY(-2px);
        }
        
        .server-button:active {
            transform: translateY(0);
        }
        
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }
        
        .instructions h3 {
            color: #495057;
            margin-top: 0;
        }
        
        .instructions ol {
            color: #6c757d;
        }
        
        .instructions li {
            margin: 10px 0;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 تشغيل السيرفر المحلي</h1>
        
        <div class="success">
            <strong>✅ السيرفر جاهز للتشغيل!</strong><br>
            يمكنك الآن فتح التطبيق في المتصفح
        </div>
        
        <a href="index.html" class="server-button" target="_blank">
            🌐 فتح التطبيق الرئيسي
        </a>
        
        <a href="test-darkness-duration.html" class="server-button" target="_blank">
            🧪 اختبار مدة التعتيم
        </a>
        
        <a href="debug-main-app.html" class="server-button" target="_blank">
            🔍 تشخيص التطبيق الرئيسي
        </a>

        <a href="test-darkness-fix.html" class="server-button" target="_blank" style="background: linear-gradient(45deg, #FF5722, #E64A19);">
            🔧 اختبار إصلاح مدة التعتيم
        </a>

        <a href="test-adhan-now.html" class="server-button" target="_blank" style="background: linear-gradient(45deg, #F44336, #D32F2F);">
            🔔 اختبار الأذان الفوري
        </a>
        
        <div class="instructions">
            <h3>📋 تعليمات الاستخدام:</h3>
            <ol>
                <li><strong>انقر على "فتح التطبيق الرئيسي"</strong> لفتح التطبيق كاملاً</li>
                <li><strong>اذهب للإعدادات (⚙️)</strong> في التطبيق الرئيسي</li>
                <li><strong>اضبط مدة التعتيم</strong> في قسم "مدة التعتيم بعد الإقامة"</li>
                <li><strong>احفظ الإعدادات</strong> بالنقر على "حفظ مدة التعتيم"</li>
                <li><strong>اختبر النظام</strong> بالنقر على "اختبار الأذان والإقامة"</li>
            </ol>
        </div>
        
        <div class="warning">
            <strong>⚠️ ملاحظة مهمة:</strong><br>
            تأكد من أن ملفات الصوت موجودة في مجلد <code>audio/</code> لتشغيل الأذان والإقامة بشكل صحيح.
        </div>
        
        <div class="instructions">
            <h3>🔧 للاختبار السريع:</h3>
            <ol>
                <li><strong>استخدم ملف الاختبار البسيط</strong> للتأكد من عمل النظام</li>
                <li><strong>اضبط مدد قصيرة</strong> (10-30 ثانية) للاختبار السريع</li>
                <li><strong>راقب Console (F12)</strong> لرؤية رسائل التشخيص</li>
            </ol>
        </div>
        
        <script>
            // تحديث الوقت كل ثانية
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA');
                document.title = `السيرفر المحلي - ${timeString}`;
            }
            
            setInterval(updateTime, 1000);
            updateTime();
            
            // رسالة ترحيب
            console.log('🚀 السيرفر المحلي يعمل بنجاح!');
            console.log('📂 المجلد الحالي:', window.location.href);
            console.log('🌐 يمكنك الآن فتح التطبيق');
        </script>
    </div>
</body>
</html>
