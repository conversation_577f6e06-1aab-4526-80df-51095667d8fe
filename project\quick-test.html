<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - الإقامة والتعتيم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .setting {
            margin: 15px 0;
        }
        
        .setting label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .setting input {
            width: 80px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .test-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        
        .test-button:hover {
            background-color: #45a049;
        }
        
        .test-button.danger {
            background-color: #f44336;
        }
        
        .test-button.danger:hover {
            background-color: #da190b;
        }
        
        #overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 1s ease;
        }
        
        #countdown {
            color: white;
            font-size: 10vw;
            font-weight: bold;
            text-align: center;
            direction: rtl;
        }
        
        #digital-clock {
            color: white;
            font-size: 20vw;
            font-weight: bold;
            text-align: center;
            direction: ltr;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
            display: none;
        }
        
        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 24px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
        }
        
        #log {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار سريع - الإقامة والتعتيم</h1>
        
        <div class="setting">
            <label>مدة الإقامة (بالدقائق):</label>
            <input type="number" id="iqama-duration" value="1" min="1" max="10">
            <small>للاختبار السريع استخدم 1 دقيقة</small>
        </div>
        
        <div class="setting">
            <label>مدة التعتيم (بالدقائق):</label>
            <input type="number" id="darkness-duration" value="2" min="1" max="30">
            <small>للاختبار السريع استخدم 2 دقيقة</small>
        </div>
        
        <button class="test-button" onclick="startTest()">🚀 بدء الاختبار</button>
        <button class="test-button danger" onclick="stopTest()">⏹️ إيقاف الاختبار</button>
        
        <div id="log"></div>
    </div>
    
    <!-- شاشة التعتيم -->
    <div id="overlay">
        <button class="close-btn" onclick="stopTest()">×</button>
        <div id="countdown"></div>
        <div id="digital-clock"></div>
    </div>
    
    <!-- ملفات الصوت -->
    <audio id="iqama-audio" src="audio/short_iqama.mp3"></audio>
    
    <script>
        let testTimer = null;
        let clockTimer = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function startTest() {
            log('🚀 بدء الاختبار...');
            
            // الحصول على القيم
            const iqamaDuration = parseInt(document.getElementById('iqama-duration').value) || 1;
            const darknessDuration = parseInt(document.getElementById('darkness-duration').value) || 2;
            
            log(`⏱️ مدة الإقامة: ${iqamaDuration} دقيقة`);
            log(`🌙 مدة التعتيم: ${darknessDuration} دقيقة`);
            
            // إظهار شاشة التعتيم
            const overlay = document.getElementById('overlay');
            const countdown = document.getElementById('countdown');
            const digitalClock = document.getElementById('digital-clock');
            
            overlay.style.display = 'flex';
            overlay.style.opacity = '1';
            countdown.style.display = 'block';
            digitalClock.style.display = 'none';
            
            // بدء العد التنازلي للإقامة
            let secondsLeft = iqamaDuration * 60;
            log(`⏰ بدء العد التنازلي: ${secondsLeft} ثانية`);
            
            const updateCountdown = () => {
                const minutes = Math.floor(secondsLeft / 60);
                const seconds = secondsLeft % 60;
                
                countdown.innerHTML = `
                    <div style="margin-bottom: 20px; font-size: 5vw;">صلاة العصر</div>
                    <div style="font-size: 15vw; margin-bottom: 20px;">
                        ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}
                    </div>
                    <div style="font-size: 4vw;">الوقت المتبقي للإقامة</div>
                `;
                
                secondsLeft--;
                
                if (secondsLeft < 0) {
                    clearInterval(testTimer);
                    log('⏰ انتهى العد التنازلي للإقامة');
                    
                    // تشغيل صوت الإقامة
                    const iqamaAudio = document.getElementById('iqama-audio');
                    if (iqamaAudio) {
                        iqamaAudio.play().then(() => {
                            log('🔊 تم تشغيل صوت الإقامة');
                        }).catch(err => {
                            log('❌ خطأ في تشغيل صوت الإقامة: ' + err.message);
                        });
                    } else {
                        log('❌ ملف صوت الإقامة غير موجود');
                    }
                    
                    // بدء التعتيم بعد ثانيتين
                    setTimeout(() => {
                        startDarkness(darknessDuration);
                    }, 2000);
                    
                    return;
                }
            };
            
            testTimer = setInterval(updateCountdown, 1000);
            updateCountdown(); // تحديث فوري
        }
        
        function startDarkness(darknessDuration) {
            log(`🌙 بدء التعتيم لمدة ${darknessDuration} دقيقة`);
            
            const countdown = document.getElementById('countdown');
            const digitalClock = document.getElementById('digital-clock');
            
            // إخفاء العد التنازلي وإظهار الساعة الرقمية
            countdown.style.display = 'none';
            digitalClock.style.display = 'block';
            
            // تحديث الساعة الرقمية
            const updateClock = () => {
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                digitalClock.textContent = `${hours}:${minutes}:${seconds}`;
            };
            
            clockTimer = setInterval(updateClock, 1000);
            updateClock(); // تحديث فوري
            
            // إنهاء التعتيم بعد المدة المحددة
            setTimeout(() => {
                stopTest();
                log('🌅 انتهى التعتيم تلقائياً');
            }, darknessDuration * 60 * 1000);
        }
        
        function stopTest() {
            log('⏹️ تم إيقاف الاختبار');
            
            // إيقاف المؤقتات
            if (testTimer) {
                clearInterval(testTimer);
                testTimer = null;
            }
            
            if (clockTimer) {
                clearInterval(clockTimer);
                clockTimer = null;
            }
            
            // إخفاء الشاشة
            const overlay = document.getElementById('overlay');
            const countdown = document.getElementById('countdown');
            const digitalClock = document.getElementById('digital-clock');
            
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.style.display = 'none';
                countdown.style.display = 'block';
                digitalClock.style.display = 'none';
            }, 1000);
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ تم تحميل صفحة الاختبار السريع');
            log('💡 نصيحة: استخدم مدد قصيرة للاختبار السريع');
        });
    </script>
</body>
</html>
