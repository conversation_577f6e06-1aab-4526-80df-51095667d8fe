name: Auto Update

on:
  push:
    branches: [ main ]
  schedule:
    - cron: '0 */6 * * *'  # كل 6 ساعات

jobs:
  update:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Auto Update
        run: |
          git config --global user.name 'GitHub Actions'
          git config --global user.email '<EMAIL>'
          git remote update
          git pull
          
      - name: Push changes
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: ${{ github.ref }}
