<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار النهائي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        
        .setting {
            margin: 15px 0;
            text-align: right;
        }
        
        .setting label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        .setting input {
            width: 80px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
            font-size: 16px;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px 5px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
            width: 100%;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-button.main-app {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        #log {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
            border: 1px solid #ddd;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 الاختبار النهائي</h1>
        
        <div class="status success">
            <strong>✅ تم إصلاح جميع المشاكل!</strong><br>
            الآن يمكن اختبار النظام بالقيم المدخلة يدو<|im_start|>
        </div>
        
        <div class="test-section">
            <h3>⚙️ إعدادات الاختبار</h3>
            
            <div class="setting">
                <label>مدة الإقامة:</label>
                <input type="number" id="asr-iqama-duration" value="2" min="1" max="40"> دقيقة
            </div>
            
            <div class="setting">
                <label>مدة التعتيم:</label>
                <input type="number" id="asr-darkness" value="3" min="0" max="60"> دقيقة
            </div>
            
            <button class="test-button" onclick="testSystem()">🧪 اختبار النظام المحدث</button>
        </div>
        
        <div class="test-section">
            <h3>🌐 التطبيق الرئيسي</h3>
            <div class="status info">
                <strong>💡 للاختبار الكامل:</strong><br>
                افتح التطبيق الرئيسي واختبر من الإعدادات
            </div>
            <button class="test-button main-app" onclick="openMainApp()">🌐 فتح التطبيق الرئيسي</button>
        </div>
        
        <div class="status warning">
            <strong>📋 خطوات الاختبار:</strong><br>
            1. اضبط مدة الإقامة والتعتيم أعلاه<br>
            2. انقر "اختبار النظام المحدث"<br>
            3. راقب العد التنازلي للإقامة (2 دقيقة)<br>
            4. راقب التعتيم مع الساعة الرقمية (3 دقائق)<br>
            5. تأكد من انتهاء التعتيم بعد المدة المحددة
        </div>
        
        <div id="log"></div>
    </div>
    
    <!-- عناصر الصوت -->
    <audio id="adhan-audio" src="audio/short_azan.mp3"></audio>
    <audio id="iqama-audio" src="audio/short_iqama.mp3"></audio>
    
    <!-- تضمين ملف العد التنازلي -->
    <script src="iqama-countdown.js"></script>
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function testSystem() {
            log('🎯 بدء الاختبار النهائي للنظام...');
            
            // قراءة القيم من الحقول
            const iqamaMinutes = parseInt(document.getElementById('asr-iqama-duration').value) || 2;
            const darknessMinutes = parseInt(document.getElementById('asr-darkness').value) || 3;
            
            log(`⚙️ إعدادات الاختبار:`);
            log(`   - مدة الإقامة: ${iqamaMinutes} دقيقة`);
            log(`   - مدة التعتيم: ${darknessMinutes} دقيقة`);
            
            // تحديث المتغيرات العالمية
            window.iqamahTimes = { asr: iqamaMinutes };
            window.darknessTimes = { asr: darknessMinutes };
            
            // حفظ في التخزين المحلي
            localStorage.setItem('iqama_durations', JSON.stringify(window.iqamahTimes));
            localStorage.setItem('darknessTimes', JSON.stringify(window.darknessTimes));
            
            log('💾 تم حفظ الإعدادات في التخزين المحلي');
            
            // بدء الاختبار
            log('🚀 بدء العد التنازلي للإقامة...');
            
            try {
                startFullScreenIqamaCountdown('asr', 'العصر');
                log('✅ تم بدء العد التنازلي بنجاح');
                log('⏰ راقب الشاشة للعد التنازلي والتعتيم');
            } catch (error) {
                log(`❌ خطأ في بدء العد التنازلي: ${error.message}`);
            }
        }
        
        function openMainApp() {
            window.open('index.html', '_blank');
            log('🌐 تم فتح التطبيق الرئيسي في نافذة جديدة');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ تم تحميل صفحة الاختبار النهائي');
            log('🎯 جاهز للاختبار مع القيم المدخلة يدو<|im_start|>');
            log('💡 نصيحة: استخدم مدد قصيرة للاختبار السريع');
        });
    </script>
</body>
</html>
