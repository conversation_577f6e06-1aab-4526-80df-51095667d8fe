// دالة لبدء العد التنازلي للإقامة في منتصف الشاشة
function startFullScreenIqamaCountdown(prayerName, arabicName) {
    console.log(`بدء العد التنازلي للإقامة لصلاة ${arabicName} (${prayerName})...`);

    // إلغاء أي مؤقت سابق للإقامة
    if (window.iqamaCountdownTimer) {
        clearInterval(window.iqamaCountdownTimer);
        window.iqamaCountdownTimer = null;
    }

    // الحصول على مدة الإقامة بالدقائق
    let iqamaMinutes = 10; // القيمة الافتراضية

    try {
        // محاولة الحصول على مدة الإقامة من التخزين المحلي
        const savedDurations = JSON.parse(localStorage.getItem('iqama_durations')) || {};
        if (savedDurations && savedDurations[prayerName]) {
            iqamaMinutes = savedDurations[prayerName];
            console.log(`تم استخدام مدة الإقامة المحفوظة: ${iqamaMinutes} دقيقة`);
        } else {
            // محاولة الحصول على مدة الإقامة من حقول الإدخال
            const inputElement = document.getElementById(`${prayerName}-iqama-duration`);
            if (inputElement) {
                iqamaMinutes = parseInt(inputElement.value) || 10;
                console.log(`تم استخدام مدة الإقامة من حقل الإدخال: ${iqamaMinutes} دقيقة`);
            } else {
                console.log(`استخدام القيمة الافتراضية لمدة الإقامة: ${iqamaMinutes} دقيقة`);
            }
        }
    } catch (error) {
        console.error(`خطأ في الحصول على مدة الإقامة: ${error.message}`);
    }

    // تحويل إلى ثواني
    let secondsLeft = iqamaMinutes * 60;

    // إنشاء عنصر التعتيم إذا لم يكن موجودًا
    let darknessOverlay = document.getElementById('darkness-overlay');
    if (!darknessOverlay) {
        darknessOverlay = document.createElement('div');
        darknessOverlay.id = 'darkness-overlay';
        darknessOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 1s ease;
        `;
        document.body.appendChild(darknessOverlay);
    } else {
        // إزالة أي محتوى سابق
        while (darknessOverlay.firstChild) {
            darknessOverlay.removeChild(darknessOverlay.firstChild);
        }
    }

    // إنشاء عنصر العد التنازلي
    const countdownDisplay = document.createElement('div');
    countdownDisplay.id = 'fullscreen-countdown';
    countdownDisplay.style.cssText = `
        color: white;
        font-size: 10vw;
        font-weight: bold;
        text-align: center;
        direction: rtl;
        font-family: 'Amiri', 'Traditional Arabic', Arial, sans-serif;
    `;
    darknessOverlay.appendChild(countdownDisplay);

    // إضافة زر إغلاق
    const closeButton = document.createElement('button');
    closeButton.style.cssText = `
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 24px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
    `;
    closeButton.innerHTML = '×';
    closeButton.onclick = function() {
        // إلغاء العد التنازلي
        if (window.iqamaCountdownTimer) {
            clearInterval(window.iqamaCountdownTimer);
            window.iqamaCountdownTimer = null;
        }

        // إخفاء التعتيم
        darknessOverlay.style.opacity = '0';
        setTimeout(() => {
            darknessOverlay.style.display = 'none';

            // إعادة تشغيل النص المتغير
            if (typeof startTextChange === 'function') {
                startTextChange();
            }
        }, 1000);
    };
    darknessOverlay.appendChild(closeButton);

    // إظهار التعتيم
    darknessOverlay.style.display = 'flex';
    darknessOverlay.style.opacity = '1';

    // إيقاف النص المتغير
    if (window.textChangeInterval) {
        clearInterval(window.textChangeInterval);
        window.textChangeInterval = null;
        console.log('تم إيقاف النص المتغير');
    }

    // تحديث العد التنازلي
    function updateCountdown() {
        const minutes = Math.floor(secondsLeft / 60);
        const seconds = secondsLeft % 60;

        countdownDisplay.innerHTML = `
            <div style="margin-bottom: 20px; font-size: 5vw;">صلاة ${arabicName}</div>
            <div style="font-size: 15vw; margin-bottom: 20px;">
                ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}
            </div>
            <div style="font-size: 4vw;">الوقت المتبقي للإقامة</div>
        `;

        if (secondsLeft <= 0) {
            clearInterval(window.iqamaCountdownTimer);
            window.iqamaCountdownTimer = null;

            // تحديث النص عند انتهاء العد التنازلي
            countdownDisplay.innerHTML = `
                <div style="margin-bottom: 20px; font-size: 5vw;">صلاة ${arabicName}</div>
                <div style="font-size: 15vw; margin-bottom: 20px;">00:00</div>
                <div style="font-size: 4vw;">حان وقت الإقامة</div>
            `;

            // تشغيل صوت الإقامة
            const iqamaAudio = document.getElementById('iqama-audio');
            if (iqamaAudio) {
                iqamaAudio.play()
                    .then(() => {
                        console.log('بدأ تشغيل صوت الإقامة');

                        // عرض الساعة الرقمية بعد ثانيتين
                        setTimeout(() => {
                            showDigitalClock(darknessOverlay);
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('خطأ في تشغيل صوت الإقامة:', err);

                        // عرض الساعة الرقمية في حالة الخطأ
                        showDigitalClock(darknessOverlay);
                    });
            } else {
                console.error('عنصر صوت الإقامة غير موجود');

                // عرض الساعة الرقمية في حالة عدم وجود صوت الإقامة
                showDigitalClock(darknessOverlay);
            }
        }

        secondsLeft--;
    }

    // تحديث العد التنازلي مباشرة ثم كل ثانية
    updateCountdown();
    window.iqamaCountdownTimer = setInterval(updateCountdown, 1000);
}

// دالة لعرض الساعة الرقمية على كامل الشاشة
function showDigitalClock(darknessOverlay) {
    console.log('عرض الساعة الرقمية على كامل الشاشة');

    // إزالة أي محتوى سابق
    while (darknessOverlay.firstChild) {
        darknessOverlay.removeChild(darknessOverlay.firstChild);
    }

    // إنشاء عداد مدة التعتيم في أعلى يمين الشاشة
    const darknessTimer = document.createElement('div');
    darknessTimer.id = 'iqama-darkness-timer';
    darknessTimer.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        color: white;
        font-size: 16px;
        font-family: Arial, sans-serif;
        background-color: rgba(0, 0, 0, 0.7);
        padding: 10px 15px;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        z-index: 10001;
        direction: rtl;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    `;
    darknessOverlay.appendChild(darknessTimer);

    // إنشاء عنصر الساعة الرقمية
    const digitalClock = document.createElement('div');
    digitalClock.id = 'fullscreen-digital-clock';
    digitalClock.style.cssText = `
        color: white;
        font-size: 20vw;
        font-weight: bold;
        text-align: center;
        direction: ltr;
        font-family: 'Digital-7', 'Courier New', monospace;
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
    `;
    darknessOverlay.appendChild(digitalClock);

    // إضافة زر إغلاق
    const closeButton = document.createElement('button');
    closeButton.style.cssText = `
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 24px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
    `;
    closeButton.innerHTML = '×';
    closeButton.onclick = function() {
        // إلغاء تحديث الساعة
        if (window.digitalClockInterval) {
            clearInterval(window.digitalClockInterval);
            window.digitalClockInterval = null;
        }

        // إخفاء التعتيم
        darknessOverlay.style.opacity = '0';
        setTimeout(() => {
            darknessOverlay.style.display = 'none';

            // إعادة تشغيل النص المتغير
            if (typeof startTextChange === 'function') {
                startTextChange();
            }
        }, 1000);
    };
    darknessOverlay.appendChild(closeButton);

    // تحديث الساعة الرقمية بنظام 12 ساعة
    function updateDigitalClock() {
        const now = new Date();
        const hours = now.getHours();
        const minutes = now.getMinutes();
        const seconds = now.getSeconds();

        // تحويل إلى نظام 12 ساعة
        const isPM = hours >= 12;
        let hours12 = hours % 12;
        if (hours12 === 0) hours12 = 12;

        const timeString = `${String(hours12).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')} ${isPM ? 'م' : 'ص'}`;
        digitalClock.textContent = timeString;
    }

    // تحديث الساعة مباشرة ثم كل ثانية
    updateDigitalClock();

    // إلغاء أي مؤقت سابق للساعة الرقمية
    if (window.digitalClockInterval) {
        clearInterval(window.digitalClockInterval);
    }

    // بدء تحديث الساعة كل ثانية
    window.digitalClockInterval = setInterval(updateDigitalClock, 1000);

    // الحصول على مدة التعتيم المحفوظة (10 دقائق افتراضيًا)
    let darknessMinutes = 10;

    console.log(`🔍 البحث عن مدة التعتيم لصلاة ${prayerName}...`);

    // محاولة قراءة من الحقول المباشرة أولاً
    const darknessInput = document.getElementById(`${prayerName}-darkness`);
    console.log(`🔍 عنصر الحقل:`, darknessInput);
    console.log(`🔍 قيمة الحقل:`, darknessInput ? darknessInput.value : 'غير موجود');

    if (darknessInput && darknessInput.value !== '') {
        const inputValue = parseInt(darknessInput.value);
        if (!isNaN(inputValue) && inputValue >= 0) {
            darknessMinutes = inputValue;
            console.log(`✅ تم الحصول على مدة التعتيم من الحقل: ${darknessMinutes} دقيقة`);
        } else {
            console.log(`❌ قيمة الحقل غير صالحة: ${darknessInput.value}`);
        }
    } else {
        // محاولة قراءة من المتغير العالمي
        if (window.darknessTimes && window.darknessTimes[prayerName] !== undefined) {
            darknessMinutes = window.darknessTimes[prayerName];
            console.log(`✅ تم الحصول على مدة التعتيم من المتغير العالمي: ${darknessMinutes} دقيقة`);
        } else {
            // محاولة قراءة من التخزين المحلي
            try {
                const savedDarknessTimes = JSON.parse(localStorage.getItem('darknessTimes')) || {};
                if (savedDarknessTimes[prayerName] !== undefined) {
                    darknessMinutes = savedDarknessTimes[prayerName];
                    console.log(`✅ تم الحصول على مدة التعتيم من التخزين المحلي: ${darknessMinutes} دقيقة`);
                } else {
                    console.log(`⚠️ استخدام مدة التعتيم الافتراضية: ${darknessMinutes} دقيقة`);
                }
            } catch (error) {
                console.log(`❌ خطأ في قراءة التخزين المحلي: ${error.message}`);
                console.log(`⚠️ استخدام مدة التعتيم الافتراضية: ${darknessMinutes} دقيقة`);
            }
        }
    }

    console.log(`🎯 مدة التعتيم النهائية لصلاة ${prayerName}: ${darknessMinutes} دقيقة`);

    // تحديث عداد التعتيم
    let remainingSeconds = darknessMinutes * 60;
    darknessTimer.textContent = `مدة التعتيم: ${darknessMinutes}:00`;

    // بدء العد التنازلي لمدة التعتيم
    const darknessCountdownInterval = setInterval(() => {
        remainingSeconds--;

        if (remainingSeconds <= 0) {
            clearInterval(darknessCountdownInterval);

            // إلغاء تحديث الساعة
            if (window.digitalClockInterval) {
                clearInterval(window.digitalClockInterval);
                window.digitalClockInterval = null;
            }

            // إخفاء التعتيم
            darknessOverlay.style.opacity = '0';
            setTimeout(() => {
                darknessOverlay.style.display = 'none';

                // إعادة تشغيل النص المتغير
                if (typeof startTextChange === 'function') {
                    startTextChange();
                }
            }, 1000);
        } else {
            // تحديث عداد التعتيم
            const minutes = Math.floor(remainingSeconds / 60);
            const seconds = remainingSeconds % 60;
            darknessTimer.textContent = `مدة التعتيم: ${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
        }
    }, 1000);

    // إخفاء التعتيم بعد المدة المحددة (كاحتياط)
    setTimeout(() => {
        // إلغاء العد التنازلي
        clearInterval(darknessCountdownInterval);

        // إلغاء تحديث الساعة
        if (window.digitalClockInterval) {
            clearInterval(window.digitalClockInterval);
            window.digitalClockInterval = null;
        }

        // إخفاء التعتيم
        darknessOverlay.style.opacity = '0';
        setTimeout(() => {
            darknessOverlay.style.display = 'none';

            // إعادة تشغيل النص المتغير
            if (typeof startTextChange === 'function') {
                startTextChange();
            }
        }, 1000);
    }, darknessMinutes * 60 * 1000 + 5000); // إضافة 5 ثوانٍ كاحتياط
}

// دالة لاختبار العد التنازلي للإقامة
function testIqamaCountdown() {
    console.log('اختبار العد التنازلي للإقامة...');

    // بدء العد التنازلي للإقامة
    startFullScreenIqamaCountdown('asr', 'العصر');
}
