<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - المغرب</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4CAF50; }
        .error { background: #ffebee; border-color: #f44336; }
        .info { background: #e3f2fd; border-color: #2196F3; }
        button { padding: 10px 20px; margin: 5px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-btn { background: #FF9800; }
        .danger-btn { background: #f44336; }
        input[type="number"] { width: 80px; padding: 5px; margin: 0 10px; }
        .log { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕌 اختبار سريع - إعدادات المغرب</h1>
        
        <div class="section info">
            <h3>📋 فحص الإعدادات الحالية</h3>
            <button onclick="checkCurrentSettings()">فحص الإعدادات</button>
            <button onclick="clearAllSettings()" class="danger-btn">مسح جميع الإعدادات</button>
            <div id="current-settings" class="log"></div>
        </div>

        <div class="section">
            <h3>⚙️ تعيين إعدادات المغرب</h3>
            <label>مدة التعتيم للمغرب (دقائق): 
                <input type="number" id="maghrib-darkness" min="0" max="60" value="5">
            </label>
            <br><br>
            <button onclick="saveMaghribSettings()">حفظ إعدادات المغرب</button>
            <button onclick="testMaghribDarkness()" class="test-btn">اختبار تعتيم المغرب</button>
        </div>

        <div class="section">
            <h3>🧪 محاكاة وقت المغرب</h3>
            <p>هذا سيحاكي ما يحدث عند دخول وقت المغرب</p>
            <button onclick="simulateMaghribTime()" class="test-btn">محاكاة المغرب</button>
            <button onclick="stopSimulation()" class="danger-btn">إيقاف المحاكاة</button>
        </div>

        <div class="section">
            <h3>📊 سجل الأحداث</h3>
            <button onclick="clearLog()">مسح السجل</button>
            <div id="event-log" class="log"></div>
        </div>
    </div>

    <script>
        let simulationInterval = null;

        function log(message) {
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function checkCurrentSettings() {
            const settingsDiv = document.getElementById('current-settings');
            let html = '<h4>الإعدادات المحفوظة:</h4>';
            
            // فحص darknessTimes
            const darknessTimes = localStorage.getItem('darknessTimes');
            if (darknessTimes) {
                try {
                    const times = JSON.parse(darknessTimes);
                    html += `<strong>darknessTimes:</strong><br>${JSON.stringify(times, null, 2)}<br><br>`;
                    
                    // تحديث الحقل
                    if (times.maghrib !== undefined) {
                        document.getElementById('maghrib-darkness').value = times.maghrib;
                    }
                } catch (e) {
                    html += `<span style="color: red;">خطأ في قراءة darknessTimes: ${e.message}</span><br><br>`;
                }
            } else {
                html += '<span style="color: orange;">لا توجد إعدادات darknessTimes محفوظة</span><br><br>';
            }
            
            // فحص المتغير العالمي
            if (window.darknessTimes) {
                html += `<strong>window.darknessTimes:</strong><br>${JSON.stringify(window.darknessTimes, null, 2)}<br><br>`;
            } else {
                html += '<span style="color: orange;">المتغير العالمي window.darknessTimes غير موجود</span><br><br>';
            }
            
            settingsDiv.innerHTML = html;
            log('تم فحص الإعدادات الحالية');
        }

        function saveMaghribSettings() {
            const maghribValue = parseInt(document.getElementById('maghrib-darkness').value) || 0;
            
            // إنشاء كائن الإعدادات
            const darknessTimes = {
                fajr: 10,
                dhuhr: 10,
                asr: 10,
                maghrib: maghribValue,
                isha: 10
            };
            
            // حفظ في localStorage
            localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
            
            // تحديث المتغير العالمي
            window.darknessTimes = darknessTimes;
            
            log(`✅ تم حفظ إعدادات المغرب: ${maghribValue} دقيقة`);
            
            // تحديث العرض
            checkCurrentSettings();
            
            alert(`تم حفظ مدة التعتيم للمغرب: ${maghribValue} دقيقة`);
        }

        function testMaghribDarkness() {
            const maghribValue = parseInt(document.getElementById('maghrib-darkness').value) || 0;
            
            if (maghribValue <= 0) {
                alert('يرجى تعيين مدة تعتيم أكبر من 0 للمغرب');
                return;
            }
            
            log(`🧪 بدء اختبار تعتيم المغرب لمدة ${maghribValue} دقيقة`);
            
            // محاكاة منطق الحصول على مدة التعتيم
            let darknessMinutes = 10; // القيمة الافتراضية
            
            // المصدر الأول: الحقول المباشرة
            const darknessInput = document.getElementById('maghrib-darkness');
            if (darknessInput && darknessInput.value !== '') {
                const inputValue = parseInt(darknessInput.value);
                if (!isNaN(inputValue) && inputValue >= 0) {
                    darknessMinutes = inputValue;
                    log(`✅ تم الحصول على مدة التعتيم من الحقل: ${darknessMinutes} دقيقة`);
                }
            }
            
            // المصدر الثاني: المتغير العالمي
            if (darknessMinutes === 10 && window.darknessTimes?.maghrib) {
                darknessMinutes = window.darknessTimes.maghrib;
                log(`✅ تم الحصول على مدة التعتيم من المتغير العالمي: ${darknessMinutes} دقيقة`);
            }
            
            // المصدر الثالث: localStorage مباشرة
            if (darknessMinutes === 10) {
                try {
                    const savedTimes = localStorage.getItem('darknessTimes');
                    if (savedTimes) {
                        const times = JSON.parse(savedTimes);
                        if (times.maghrib) {
                            darknessMinutes = times.maghrib;
                            log(`✅ تم الحصول على مدة التعتيم من localStorage: ${darknessMinutes} دقيقة`);
                        }
                    }
                } catch (e) {
                    log(`❌ خطأ في قراءة localStorage: ${e.message}`);
                }
            }
            
            log(`🎯 مدة التعتيم النهائية للمغرب: ${darknessMinutes} دقيقة`);
            
            // محاكاة التعتيم
            if (darknessMinutes > 0) {
                log(`⏰ سيتم إنهاء التعتيم بعد ${darknessMinutes} دقيقة`);
                setTimeout(() => {
                    log(`✅ انتهى اختبار التعتيم للمغرب`);
                }, darknessMinutes * 1000); // استخدام ثوان بدلاً من دقائق للاختبار السريع
            } else {
                log(`⚠️ مدة التعتيم 0 - لن يتم تفعيل التعتيم`);
            }
        }

        function simulateMaghribTime() {
            log('🕌 محاكاة دخول وقت المغرب...');
            
            // حفظ الإعدادات أولاً
            saveMaghribSettings();
            
            // محاكاة تشغيل الأذان
            log('🔔 تشغيل أذان المغرب...');
            
            setTimeout(() => {
                log('🎵 انتهى الأذان، بدء العد التنازلي للإقامة...');
                
                setTimeout(() => {
                    log('📢 تشغيل الإقامة...');
                    
                    setTimeout(() => {
                        log('🌑 بدء التعتيم...');
                        testMaghribDarkness();
                    }, 1000);
                }, 2000);
            }, 1000);
        }

        function stopSimulation() {
            if (simulationInterval) {
                clearInterval(simulationInterval);
                simulationInterval = null;
            }
            log('⏹️ تم إيقاف المحاكاة');
        }

        function clearAllSettings() {
            if (confirm('هل أنت متأكد من مسح جميع الإعدادات؟')) {
                localStorage.removeItem('darknessTimes');
                localStorage.removeItem('darkness_durations');
                window.darknessTimes = undefined;
                log('🗑️ تم مسح جميع الإعدادات');
                checkCurrentSettings();
            }
        }

        function clearLog() {
            document.getElementById('event-log').innerHTML = '';
        }

        // تحميل الإعدادات عند فتح الصفحة
        window.onload = function() {
            log('📱 تم تحميل صفحة الاختبار');
            checkCurrentSettings();
        };
    </script>
</body>
</html>
