# دليل تشخيص مشاكل الإقامة والتعتيم

## 🔧 المشاكل التي تم إصلاحها:

### ✅ 1. مشكلة عدم تطبيق الوقت المدخل يدوياً:
- **السبب:** الكود كان يتوقف قبل قراءة الحقول
- **الحل:** تم إزالة `return` المبكر وإصلاح منطق قراءة الحقول
- **التحقق:** افتح Console (F12) وابحث عن رسائل مثل:
  ```
  🔍 البحث عن مدة الإقامة لصلاة asr...
  ✅ تم استخدام مدة الإقامة من حقل الإدخال: 5 دقيقة
  ```

### ✅ 2. مشكلة عدم وجود التعتيم:
- **السبب:** لم تكن هناك مستمعات للأزرار
- **الحل:** تم إضافة مستمعات لأزرار الحفظ والتحميل
- **التحقق:** افتح Console وابحث عن رسائل مثل:
  ```
  🔍 البحث عن مدة التعتيم لصلاة asr...
  ✅ تم الحصول على مدة التعتيم من حقل الإدخال: 3 دقيقة
  ```

## 🧪 كيفية الاختبار:

### الطريقة الأولى - ملف الاختبار المخصص:
1. افتح `test-iqama-darkness.html`
2. اضبط مدة الإقامة (مثلاً 1 دقيقة للاختبار السريع)
3. اضبط مدة التعتيم (مثلاً 2 دقيقة للاختبار السريع)
4. انقر "اختبار إقامة العصر"
5. راقب:
   - العد التنازلي للإقامة (1 دقيقة)
   - تشغيل صوت الإقامة
   - بدء التعتيم مع الساعة الرقمية (2 دقيقة)

### الطريقة الثانية - الملف الرئيسي:
1. افتح `index.html`
2. انقر على الإعدادات (⚙️)
3. اذهب إلى قسم "إعدادات الإقامة والتعتيم"
4. اضبط المدد:
   - مدة الإقامة: 1-2 دقيقة للاختبار
   - مدة التعتيم: 2-3 دقائق للاختبار
5. انقر "حفظ مدة الإقامة" و "حفظ مدة التعتيم"
6. انقر "اختبار الأذان والإقامة"

## 🔍 رسائل التشخيص في Console:

### رسائل الإقامة:
```
🔍 البحث عن مدة الإقامة لصلاة asr...
🔍 البحث عن عنصر: asr-iqama-duration
✅ تم استخدام مدة الإقامة من حقل الإدخال: 2 دقيقة
⏱️ بدء العد التنازلي: 2 دقيقة (120 ثانية)
```

### رسائل التعتيم:
```
🔍 البحث عن مدة التعتيم لصلاة asr...
🔍 البحث عن عنصر: asr-darkness-duration
✅ تم الحصول على مدة التعتيم من حقل الإدخال: 3 دقيقة
🌙 بدء التعتيم لمدة 3 دقيقة
```

## ❌ إذا لم يعمل شيء:

### 1. تحقق من وجود الحقول:
افتح Console واكتب:
```javascript
console.log(document.getElementById('asr-iqama-duration'));
console.log(document.getElementById('asr-darkness-duration'));
```
يجب أن ترى عناصر HTML وليس `null`

### 2. تحقق من القيم:
```javascript
console.log(document.getElementById('asr-iqama-duration').value);
console.log(document.getElementById('asr-darkness-duration').value);
```

### 3. اختبار يدوي:
```javascript
// اختبار الإقامة مباشرة
startIqamahCountdown('asr', 'العصر');
```

## 🎯 ما يجب أن يحدث:

### عند تشغيل الإقامة:
1. **شاشة سوداء** تظهر فوراً
2. **عد تنازلي كبير** يظهر الوقت المتبقي
3. **صوت الإقامة** يُشغل عند انتهاء العد
4. **ساعة رقمية** تظهر بعد الإقامة
5. **التعتيم ينتهي** بعد المدة المحددة

### إذا لم يحدث التعتيم:
- تحقق من أن مدة التعتيم > 0
- تحقق من رسائل Console
- جرب ملف الاختبار `test-iqama-darkness.html`

## 📞 للمساعدة:
إذا استمرت المشكلة، أرسل لي:
1. رسائل Console (F12)
2. قيم الحقول التي أدخلتها
3. ما يحدث بالضبط vs ما تتوقعه

## 🚀 نصائح للاختبار السريع:
- استخدم مدد قصيرة (1-2 دقيقة) للاختبار
- افتح Console لمراقبة الرسائل
- جرب ملف الاختبار أولاً قبل الملف الرئيسي
- تأكد من حفظ الإعدادات قبل الاختبار
