<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط - الإقامة والتعتيم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 400px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .setting {
            margin: 20px 0;
            text-align: right;
        }
        
        .setting label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        
        .setting input {
            width: 80px;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            text-align: center;
        }
        
        .setting input:focus {
            border-color: #4CAF50;
            outline: none;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 15px 5px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            width: 100%;
            transition: transform 0.2s;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-button:active {
            transform: translateY(0);
        }
        
        .stop-button {
            background: linear-gradient(45deg, #f44336, #da190b);
        }
        
        #overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #000000, #1a1a1a);
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 1s ease;
        }
        
        #display {
            color: white;
            font-weight: bold;
            text-align: center;
            direction: rtl;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }
        
        .close-btn {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        #log {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
            text-align: right;
            border: 1px solid #ddd;
        }
        
        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار الإقامة والتعتيم</h1>
        
        <div class="status info">
            <strong>تعليمات:</strong><br>
            • اضبط المدد حسب رغبتك<br>
            • للاختبار السريع استخدم 10-30 ثانية<br>
            • انقر "بدء الاختبار" وراقب النتيجة
        </div>
        
        <div class="setting">
            <label>⏰ مدة الإقامة (بالثواني):</label>
            <input type="number" id="iqama-duration" value="15" min="5" max="300">
        </div>
        
        <div class="setting">
            <label>🌙 مدة التعتيم (بالثواني):</label>
            <input type="number" id="darkness-duration" value="30" min="10" max="600">
        </div>
        
        <button class="test-button" onclick="startTest()">🚀 بدء الاختبار</button>
        <button class="test-button stop-button" onclick="stopTest()">⏹️ إيقاف الاختبار</button>
        
        <div id="log"></div>
    </div>
    
    <!-- شاشة التعتيم -->
    <div id="overlay">
        <button class="close-btn" onclick="stopTest()">×</button>
        <div id="display"></div>
    </div>
    
    <script>
        let testTimer = null;
        let clockTimer = null;
        let currentPhase = '';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function startTest() {
            log('🚀 بدء الاختبار...');
            
            // الحصول على القيم
            const iqamaDuration = parseInt(document.getElementById('iqama-duration').value) || 15;
            const darknessDuration = parseInt(document.getElementById('darkness-duration').value) || 30;
            
            log(`⏱️ مدة الإقامة: ${iqamaDuration} ثانية`);
            log(`🌙 مدة التعتيم: ${darknessDuration} ثانية`);
            
            // إظهار شاشة التعتيم
            const overlay = document.getElementById('overlay');
            const display = document.getElementById('display');
            
            overlay.style.display = 'flex';
            setTimeout(() => {
                overlay.style.opacity = '1';
            }, 100);
            
            // بدء العد التنازلي للإقامة
            currentPhase = 'iqama';
            let secondsLeft = iqamaDuration;
            log(`⏰ بدء العد التنازلي للإقامة: ${secondsLeft} ثانية`);
            
            const updateCountdown = () => {
                if (currentPhase !== 'iqama') return;
                
                const minutes = Math.floor(secondsLeft / 60);
                const seconds = secondsLeft % 60;
                
                display.innerHTML = `
                    <div style="font-size: 6vw; margin-bottom: 40px; color: #FFD700;">🕌 صلاة العصر</div>
                    <div style="font-size: 20vw; margin-bottom: 40px; color: #00FF00; text-shadow: 0 0 30px rgba(0, 255, 0, 0.8);">
                        ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}
                    </div>
                    <div style="font-size: 4vw; color: #FFFFFF;">⏰ الوقت المتبقي للإقامة</div>
                `;
                
                secondsLeft--;
                
                if (secondsLeft < 0) {
                    log('⏰ انتهى العد التنازلي للإقامة');
                    log('🔊 تشغيل صوت الإقامة (محاكاة)');
                    
                    // محاكاة صوت الإقامة
                    display.innerHTML = `
                        <div style="font-size: 8vw; color: #FFD700; animation: pulse 1s infinite;">
                            🔊 الإقامة
                        </div>
                        <div style="font-size: 4vw; color: #FFFFFF; margin-top: 30px;">
                            جاري تشغيل صوت الإقامة...
                        </div>
                    `;
                    
                    // بدء التعتيم بعد 3 ثوان
                    setTimeout(() => {
                        startDarkness(darknessDuration);
                    }, 3000);
                    
                    return;
                }
                
                testTimer = setTimeout(updateCountdown, 1000);
            };
            
            updateCountdown();
        }
        
        function startDarkness(darknessDuration) {
            log(`🌙 بدء التعتيم لمدة ${darknessDuration} ثانية`);
            currentPhase = 'darkness';
            
            const display = document.getElementById('display');
            
            // تحديث الساعة الرقمية
            const updateClock = () => {
                if (currentPhase !== 'darkness') return;
                
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                
                display.innerHTML = `
                    <div style="font-size: 4vw; margin-bottom: 40px; color: #87CEEB;">🌙 وقت التعتيم</div>
                    <div style="font-size: 25vw; color: #00FFFF; text-shadow: 0 0 40px rgba(0, 255, 255, 0.9); font-family: 'Courier New', monospace;">
                        ${hours}:${minutes}:${seconds}
                    </div>
                    <div style="font-size: 3vw; margin-top: 40px; color: #FFFFFF;">
                        ⏱️ سينتهي التعتيم خلال ${Math.ceil(darknessDuration)} ثانية
                    </div>
                `;
                
                darknessDuration--;
                
                if (darknessDuration <= 0) {
                    clearInterval(clockTimer);
                    stopTest();
                    log('🌅 انتهى التعتيم - تم الاختبار بنجاح!');
                    return;
                }
                
                clockTimer = setTimeout(updateClock, 1000);
            };
            
            updateClock();
        }
        
        function stopTest() {
            log('⏹️ تم إيقاف الاختبار');
            currentPhase = '';
            
            // إيقاف المؤقتات
            if (testTimer) {
                clearTimeout(testTimer);
                testTimer = null;
            }
            
            if (clockTimer) {
                clearTimeout(clockTimer);
                clockTimer = null;
            }
            
            // إخفاء الشاشة
            const overlay = document.getElementById('overlay');
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 1000);
        }
        
        // إضافة CSS للأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ تم تحميل صفحة الاختبار البسيط');
            log('💡 نصيحة: استخدم مدد قصيرة للاختبار السريع');
            log('🎯 الهدف: اختبار العد التنازلي والتعتيم');
        });
    </script>
</body>
</html>
