<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام مواقيت الصلاة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .test-button:hover {
            background-color: #45a049;
        }
        
        .settings-group {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .settings-group h3 {
            margin-top: 0;
            color: #333;
        }
        
        .setting-item {
            margin: 10px 0;
        }
        
        .setting-item label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        .setting-item select, .setting-item input {
            width: 200px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        .prayer-times-display {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .prayer-time-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .prayer-time-item:last-child {
            border-bottom: none;
        }
        
        #log {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار نظام مواقيت الصلاة</h1>
        
        <div class="settings-group">
            <h3>إعدادات المدينة</h3>
            <div class="setting-item">
                <label>المدينة:</label>
                <select id="city-select">
                    <option value="Asia/Amman">عمان، الأردن</option>
                    <option value="Asia/Riyadh">الرياض، السعودية</option>
                    <option value="Asia/Dubai">دبي، الإمارات</option>
                    <option value="Asia/Makkah">مكة المكرمة، السعودية</option>
                    <option value="Asia/Madinah">المدينة المنورة، السعودية</option>
                    <option value="Asia/Jerusalem">القدس، فلسطين</option>
                    <option value="Asia/Baghdad">بغداد، العراق</option>
                    <option value="Africa/Cairo">القاهرة، مصر</option>
                </select>
            </div>
            <div class="setting-item">
                <label>طريقة الحساب:</label>
                <select id="calculation-method">
                    <option value="MWL">رابطة العالم الإسلامي</option>
                    <option value="ISNA">الجمعية الإسلامية لأمريكا الشمالية</option>
                    <option value="Egypt">الهيئة المصرية العامة للمساحة</option>
                    <option value="Makkah">جامعة أم القرى، مكة المكرمة</option>
                    <option value="Karachi">جامعة العلوم الإسلامية، كراتشي</option>
                </select>
            </div>
        </div>
        
        <div class="settings-group">
            <h3>التعديل اليدوي</h3>
            <div class="setting-item">
                <label>الفجر:</label>
                <input type="time" id="manual-fajr" placeholder="05:00">
            </div>
            <div class="setting-item">
                <label>الظهر:</label>
                <input type="time" id="manual-dhuhr" placeholder="12:00">
            </div>
            <div class="setting-item">
                <label>العصر:</label>
                <input type="time" id="manual-asr" placeholder="15:30">
            </div>
            <div class="setting-item">
                <label>المغرب:</label>
                <input type="time" id="manual-maghrib" placeholder="18:00">
            </div>
            <div class="setting-item">
                <label>العشاء:</label>
                <input type="time" id="manual-isha" placeholder="19:30">
            </div>
        </div>
        
        <div class="settings-group">
            <h3>الاختبارات</h3>
            <button class="test-button" onclick="testCalculatePrayerTimes()">حساب المواقيت</button>
            <button class="test-button" onclick="testSaveManualTimes()">حفظ المواقيت اليدوية</button>
            <button class="test-button" onclick="testLoadSavedTimes()">تحميل المواقيت المحفوظة</button>
            <button class="test-button" onclick="testCityChange()">اختبار تغيير المدينة</button>
            <button class="test-button" onclick="clearStorage()">مسح التخزين</button>
        </div>
        
        <div class="prayer-times-display">
            <h3>مواقيت الصلاة الحالية</h3>
            <div id="prayer-times-list">
                <div class="prayer-time-item">
                    <span>الفجر:</span>
                    <span id="fajr-time">--:--</span>
                </div>
                <div class="prayer-time-item">
                    <span>الشروق:</span>
                    <span id="sunrise-time">--:--</span>
                </div>
                <div class="prayer-time-item">
                    <span>الظهر:</span>
                    <span id="dhuhr-time">--:--</span>
                </div>
                <div class="prayer-time-item">
                    <span>العصر:</span>
                    <span id="asr-time">--:--</span>
                </div>
                <div class="prayer-time-item">
                    <span>المغرب:</span>
                    <span id="maghrib-time">--:--</span>
                </div>
                <div class="prayer-time-item">
                    <span>العشاء:</span>
                    <span id="isha-time">--:--</span>
                </div>
            </div>
        </div>
        
        <div id="log"></div>
    </div>
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function testCalculatePrayerTimes() {
            const city = document.getElementById('city-select').value;
            const method = document.getElementById('calculation-method').value;
            
            log(`حساب مواقيت الصلاة لمدينة ${city} بطريقة ${method}...`);
            
            // حفظ الإعدادات
            localStorage.setItem('selectedCity', city);
            localStorage.setItem('calculationMethod', method);
            
            // مواقيت افتراضية للاختبار
            const defaultTimes = {
                'Asia/Amman': { fajr: '05:00', sunrise: '06:30', dhuhr: '12:30', asr: '15:45', maghrib: '18:15', isha: '19:45' },
                'Asia/Riyadh': { fajr: '04:45', sunrise: '06:15', dhuhr: '12:00', asr: '15:30', maghrib: '17:45', isha: '19:15' },
                'Asia/Dubai': { fajr: '05:15', sunrise: '06:45', dhuhr: '12:20', asr: '15:35', maghrib: '18:05', isha: '19:35' }
            };
            
            const times = defaultTimes[city] || defaultTimes['Asia/Amman'];
            
            // تحديث العرض
            updatePrayerTimesDisplay(times);
            
            log(`تم حساب المواقيت: ${JSON.stringify(times)}`);
        }
        
        function testSaveManualTimes() {
            const manualTimes = {
                fajr: document.getElementById('manual-fajr').value || '05:00',
                dhuhr: document.getElementById('manual-dhuhr').value || '12:00',
                asr: document.getElementById('manual-asr').value || '15:30',
                maghrib: document.getElementById('manual-maghrib').value || '18:00',
                isha: document.getElementById('manual-isha').value || '19:30'
            };
            
            const city = document.getElementById('city-select').value;
            
            // حفظ المواقيت اليدوية
            const savedManualTimes = JSON.parse(localStorage.getItem('manualPrayerTimes') || '{}');
            savedManualTimes[city] = manualTimes;
            localStorage.setItem('manualPrayerTimes', JSON.stringify(savedManualTimes));
            localStorage.setItem('manualPrayerTimesEnabled', 'true');
            
            // تحديث العرض
            updatePrayerTimesDisplay(manualTimes);
            
            log(`تم حفظ المواقيت اليدوية للمدينة ${city}: ${JSON.stringify(manualTimes)}`);
        }
        
        function testLoadSavedTimes() {
            const city = document.getElementById('city-select').value;
            const savedManualTimes = JSON.parse(localStorage.getItem('manualPrayerTimes') || '{}');
            
            if (savedManualTimes[city]) {
                const times = savedManualTimes[city];
                
                // تحديث حقول الإدخال
                document.getElementById('manual-fajr').value = times.fajr || '';
                document.getElementById('manual-dhuhr').value = times.dhuhr || '';
                document.getElementById('manual-asr').value = times.asr || '';
                document.getElementById('manual-maghrib').value = times.maghrib || '';
                document.getElementById('manual-isha').value = times.isha || '';
                
                // تحديث العرض
                updatePrayerTimesDisplay(times);
                
                log(`تم تحميل المواقيت المحفوظة للمدينة ${city}: ${JSON.stringify(times)}`);
            } else {
                log(`لا توجد مواقيت محفوظة للمدينة ${city}`);
            }
        }
        
        function testCityChange() {
            const citySelect = document.getElementById('city-select');
            const cities = Array.from(citySelect.options).map(option => option.value);
            const currentIndex = cities.indexOf(citySelect.value);
            const nextIndex = (currentIndex + 1) % cities.length;
            
            citySelect.value = cities[nextIndex];
            
            log(`تم تغيير المدينة إلى: ${citySelect.options[citySelect.selectedIndex].text}`);
            
            // اختبار تحميل المواقيت للمدينة الجديدة
            testLoadSavedTimes();
        }
        
        function clearStorage() {
            localStorage.removeItem('selectedCity');
            localStorage.removeItem('calculationMethod');
            localStorage.removeItem('manualPrayerTimes');
            localStorage.removeItem('manualPrayerTimesEnabled');
            
            // مسح حقول الإدخال
            document.getElementById('manual-fajr').value = '';
            document.getElementById('manual-dhuhr').value = '';
            document.getElementById('manual-asr').value = '';
            document.getElementById('manual-maghrib').value = '';
            document.getElementById('manual-isha').value = '';
            
            // مسح العرض
            updatePrayerTimesDisplay({});
            
            log('تم مسح جميع البيانات المحفوظة');
        }
        
        function updatePrayerTimesDisplay(times) {
            document.getElementById('fajr-time').textContent = times.fajr || '--:--';
            document.getElementById('sunrise-time').textContent = times.sunrise || '--:--';
            document.getElementById('dhuhr-time').textContent = times.dhuhr || '--:--';
            document.getElementById('asr-time').textContent = times.asr || '--:--';
            document.getElementById('maghrib-time').textContent = times.maghrib || '--:--';
            document.getElementById('isha-time').textContent = times.isha || '--:--';
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة اختبار نظام مواقيت الصلاة');
            
            // تحميل الإعدادات المحفوظة
            const savedCity = localStorage.getItem('selectedCity');
            const savedMethod = localStorage.getItem('calculationMethod');
            
            if (savedCity) {
                document.getElementById('city-select').value = savedCity;
                log(`تم تحميل المدينة المحفوظة: ${savedCity}`);
            }
            
            if (savedMethod) {
                document.getElementById('calculation-method').value = savedMethod;
                log(`تم تحميل طريقة الحساب المحفوظة: ${savedMethod}`);
            }
            
            // تحميل المواقيت المحفوظة
            testLoadSavedTimes();
        });
    </script>
</body>
</html>
