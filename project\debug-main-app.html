<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص التطبيق الرئيسي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .debug-section h3 {
            color: #555;
            margin-top: 0;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px 5px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-button.danger {
            background: linear-gradient(45deg, #f44336, #da190b);
        }
        
        .setting {
            margin: 15px 0;
        }
        
        .setting label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        .setting input {
            width: 80px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
        }
        
        #log {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
            border: 1px solid #ddd;
        }
        
        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص التطبيق الرئيسي</h1>
        
        <div class="status warning">
            <strong>⚠️ هذه صفحة تشخيص:</strong><br>
            تساعد في اكتشاف مشاكل قراءة مدة التعتيم في التطبيق الرئيسي
        </div>
        
        <div class="debug-section">
            <h3>🧪 اختبار حقول مدة التعتيم</h3>
            
            <div class="setting">
                <label>الفجر:</label>
                <input type="number" id="fajr-darkness" value="5" min="0" max="60"> دقيقة
            </div>
            <div class="setting">
                <label>الظهر:</label>
                <input type="number" id="dhuhr-darkness" value="7" min="0" max="60"> دقيقة
            </div>
            <div class="setting">
                <label>العصر:</label>
                <input type="number" id="asr-darkness" value="10" min="0" max="60"> دقيقة
            </div>
            <div class="setting">
                <label>المغرب:</label>
                <input type="number" id="maghrib-darkness" value="8" min="0" max="60"> دقيقة
            </div>
            <div class="setting">
                <label>العشاء:</label>
                <input type="number" id="isha-darkness" value="12" min="0" max="60"> دقيقة
            </div>
            
            <button class="test-button" onclick="testReadDarknessValues()">🔍 اختبار قراءة القيم</button>
            <button class="test-button" onclick="saveDarknessValues()">💾 حفظ القيم</button>
            <button class="test-button" onclick="loadDarknessValues()">📂 تحميل القيم</button>
        </div>
        
        <div class="debug-section">
            <h3>🚀 اختبار التطبيق الرئيسي</h3>
            <button class="test-button" onclick="testMainAppFunction()">🧪 اختبار دالة التطبيق الرئيسي</button>
            <button class="test-button" onclick="openMainApp()">🌐 فتح التطبيق الرئيسي</button>
        </div>
        
        <div class="debug-section">
            <h3>🗂️ معلومات التخزين المحلي</h3>
            <button class="test-button" onclick="showStorageInfo()">📊 عرض معلومات التخزين</button>
            <button class="test-button danger" onclick="clearStorage()">🗑️ مسح التخزين</button>
        </div>
        
        <div id="log"></div>
    </div>
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function testReadDarknessValues() {
            log('🔍 اختبار قراءة قيم مدة التعتيم...');
            
            const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
            
            prayers.forEach(prayer => {
                const input = document.getElementById(`${prayer}-darkness`);
                if (input) {
                    const value = input.value;
                    const parsedValue = parseInt(value);
                    
                    log(`📝 ${prayer}: القيمة="${value}", المحولة=${parsedValue}, صالحة=${!isNaN(parsedValue) && parsedValue >= 0}`);
                } else {
                    log(`❌ ${prayer}: الحقل غير موجود`);
                }
            });
        }
        
        function saveDarknessValues() {
            log('💾 حفظ قيم مدة التعتيم...');
            
            const darknessTimes = {
                fajr: parseInt(document.getElementById('fajr-darkness').value) || 0,
                dhuhr: parseInt(document.getElementById('dhuhr-darkness').value) || 0,
                asr: parseInt(document.getElementById('asr-darkness').value) || 0,
                maghrib: parseInt(document.getElementById('maghrib-darkness').value) || 0,
                isha: parseInt(document.getElementById('isha-darkness').value) || 0
            };
            
            localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
            window.darknessTimes = darknessTimes;
            
            log(`✅ تم حفظ القيم: ${JSON.stringify(darknessTimes)}`);
        }
        
        function loadDarknessValues() {
            log('📂 تحميل قيم مدة التعتيم...');
            
            try {
                const savedData = localStorage.getItem('darknessTimes');
                if (savedData) {
                    const darknessTimes = JSON.parse(savedData);
                    
                    // تحديث الحقول
                    Object.keys(darknessTimes).forEach(prayer => {
                        const input = document.getElementById(`${prayer}-darkness`);
                        if (input) {
                            input.value = darknessTimes[prayer];
                        }
                    });
                    
                    window.darknessTimes = darknessTimes;
                    log(`✅ تم تحميل القيم: ${JSON.stringify(darknessTimes)}`);
                } else {
                    log('⚠️ لا توجد قيم محفوظة');
                }
            } catch (error) {
                log(`❌ خطأ في التحميل: ${error.message}`);
            }
        }
        
        function testMainAppFunction() {
            log('🧪 اختبار دالة التطبيق الرئيسي...');
            
            // محاكاة دالة قراءة مدة التعتيم من التطبيق الرئيسي
            const prayerName = 'asr';
            let darknessMinutes = 10; // القيمة الافتراضية
            
            log(`🔍 البحث عن مدة التعتيم لصلاة ${prayerName}...`);
            log(`🔍 البحث عن عنصر: ${prayerName}-darkness`);
            
            const darknessInput = document.getElementById(`${prayerName}-darkness`);
            log(`🔍 عنصر الحقل:`, darknessInput ? 'موجود' : 'غير موجود');
            log(`🔍 قيمة الحقل:`, darknessInput ? darknessInput.value : 'غير موجود');
            
            if (darknessInput && darknessInput.value !== '') {
                const inputValue = parseInt(darknessInput.value);
                if (!isNaN(inputValue) && inputValue >= 0) {
                    darknessMinutes = inputValue;
                    log(`✅ تم الحصول على مدة التعتيم من الحقل: ${darknessMinutes} دقيقة`);
                } else {
                    log(`❌ قيمة الحقل غير صالحة: ${darknessInput.value}`);
                }
            } else {
                if (window.darknessTimes && window.darknessTimes[prayerName]) {
                    darknessMinutes = window.darknessTimes[prayerName];
                    log(`✅ تم الحصول على مدة التعتيم من المتغير العالمي: ${darknessMinutes} دقيقة`);
                } else {
                    try {
                        const savedDarknessTimes = JSON.parse(localStorage.getItem('darknessTimes')) || {};
                        if (savedDarknessTimes[prayerName]) {
                            darknessMinutes = savedDarknessTimes[prayerName];
                            log(`✅ تم الحصول على مدة التعتيم من التخزين المحلي: ${darknessMinutes} دقيقة`);
                        } else {
                            log(`⚠️ استخدام مدة التعتيم الافتراضية: ${darknessMinutes} دقيقة`);
                        }
                    } catch (error) {
                        log(`❌ خطأ في قراءة التخزين المحلي: ${error.message}`);
                        log(`⚠️ استخدام مدة التعتيم الافتراضية: ${darknessMinutes} دقيقة`);
                    }
                }
            }
            
            log(`🎯 مدة التعتيم النهائية لصلاة ${prayerName}: ${darknessMinutes} دقيقة`);
        }
        
        function showStorageInfo() {
            log('📊 معلومات التخزين المحلي...');
            
            const keys = ['darknessTimes', 'iqama_durations', 'selectedCity', 'calculationMethod'];
            
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    try {
                        const parsed = JSON.parse(value);
                        log(`📝 ${key}: ${JSON.stringify(parsed)}`);
                    } catch {
                        log(`📝 ${key}: ${value}`);
                    }
                } else {
                    log(`❌ ${key}: غير موجود`);
                }
            });
        }
        
        function clearStorage() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات المحفوظة؟')) {
                localStorage.clear();
                log('🗑️ تم مسح جميع البيانات المحفوظة');
            }
        }
        
        function openMainApp() {
            window.open('index.html', '_blank');
            log('🌐 تم فتح التطبيق الرئيسي في نافذة جديدة');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ تم تحميل صفحة التشخيص');
            log('💡 استخدم هذه الصفحة لاختبار قراءة مدة التعتيم');
            
            // تحميل القيم المحفوظة تلقائياً
            loadDarknessValues();
        });
    </script>
</body>
</html>
