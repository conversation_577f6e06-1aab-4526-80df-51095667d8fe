<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام المبسط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .test-button:hover {
            background-color: #45a049;
        }
        
        .settings-group {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .settings-group h3 {
            margin-top: 0;
            color: #333;
        }
        
        .setting-item {
            margin: 10px 0;
        }
        
        .setting-item label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        
        .setting-item input {
            width: 60px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        #darkness-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 1s ease;
        }
        
        #countdown-display {
            color: white;
            font-size: 10vw;
            font-weight: bold;
            text-align: center;
            direction: rtl;
            font-family: Arial, sans-serif;
        }
        
        #digital-clock {
            color: white;
            font-size: 20vw;
            font-weight: bold;
            text-align: center;
            direction: ltr;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
        }
        
        .close-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 24px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار النظام المبسط</h1>
        
        <div class="settings-group">
            <h3>إعدادات الإقامة</h3>
            <div class="setting-item">
                <label>مدة الإقامة:</label>
                <input type="number" id="iqama-duration" value="1" min="1" max="10"> دقيقة
            </div>
        </div>
        
        <div class="settings-group">
            <h3>إعدادات التعتيم</h3>
            <div class="setting-item">
                <label>مدة التعتيم:</label>
                <input type="number" id="darkness-duration" value="2" min="1" max="10"> دقيقة
            </div>
        </div>
        
        <div class="settings-group">
            <h3>الاختبارات</h3>
            <button class="test-button" onclick="testIqamaCountdown()">اختبار العد التنازلي للإقامة</button>
            <button class="test-button" onclick="testDarkness()">اختبار التعتيم</button>
            <button class="test-button" onclick="testFullSystem()">اختبار النظام الكامل</button>
        </div>
        
        <div id="log" style="background: #f9f9f9; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
    </div>
    
    <!-- شاشة التعتيم -->
    <div id="darkness-overlay">
        <button class="close-button" onclick="closeDarkness()">×</button>
        <div id="countdown-display"></div>
        <div id="digital-clock" style="display: none;"></div>
    </div>
    
    <!-- ملفات الصوت -->
    <audio id="iqama-audio" src="audio/short_iqama.mp3"></audio>
    
    <script>
        let iqamahTimer = null;
        let digitalClockInterval = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function testIqamaCountdown() {
            log('بدء اختبار العد التنازلي للإقامة...');
            
            const iqamaDuration = parseInt(document.getElementById('iqama-duration').value) || 1;
            let secondsLeft = iqamaDuration * 60;
            
            const overlay = document.getElementById('darkness-overlay');
            const countdownDisplay = document.getElementById('countdown-display');
            
            // إظهار شاشة التعتيم
            overlay.style.display = 'flex';
            overlay.style.opacity = '1';
            
            // تحديث العد التنازلي
            const updateCountdown = () => {
                const minutes = Math.floor(secondsLeft / 60);
                const seconds = secondsLeft % 60;
                
                countdownDisplay.innerHTML = `
                    <div style="margin-bottom: 20px; font-size: 5vw;">صلاة العصر</div>
                    <div style="font-size: 15vw; margin-bottom: 20px;">
                        ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}
                    </div>
                    <div style="font-size: 4vw;">الوقت المتبقي للإقامة</div>
                `;
                
                secondsLeft--;
                
                if (secondsLeft < 0) {
                    clearInterval(iqamahTimer);
                    log('انتهى العد التنازلي للإقامة');
                    
                    // تشغيل صوت الإقامة
                    const iqamaAudio = document.getElementById('iqama-audio');
                    if (iqamaAudio) {
                        iqamaAudio.play().then(() => {
                            log('تم تشغيل صوت الإقامة');
                        }).catch(err => {
                            log('خطأ في تشغيل صوت الإقامة: ' + err.message);
                        });
                    }
                    
                    // بدء التعتيم
                    setTimeout(() => {
                        testDarkness();
                    }, 1000);
                }
            };
            
            // بدء العد التنازلي
            iqamahTimer = setInterval(updateCountdown, 1000);
            updateCountdown(); // تحديث فوري
        }
        
        function testDarkness() {
            log('بدء اختبار التعتيم...');
            
            const darknessDuration = parseInt(document.getElementById('darkness-duration').value) || 2;
            
            const overlay = document.getElementById('darkness-overlay');
            const countdownDisplay = document.getElementById('countdown-display');
            const digitalClock = document.getElementById('digital-clock');
            
            // إخفاء العد التنازلي وإظهار الساعة الرقمية
            countdownDisplay.style.display = 'none';
            digitalClock.style.display = 'block';
            
            // تحديث الساعة الرقمية
            const updateClock = () => {
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                digitalClock.textContent = `${hours}:${minutes}:${seconds}`;
            };
            
            // بدء تحديث الساعة
            digitalClockInterval = setInterval(updateClock, 1000);
            updateClock(); // تحديث فوري
            
            // إخفاء التعتيم بعد المدة المحددة
            setTimeout(() => {
                closeDarkness();
                log('انتهى التعتيم');
            }, darknessDuration * 60 * 1000);
        }
        
        function testFullSystem() {
            log('بدء اختبار النظام الكامل...');
            testIqamaCountdown();
        }
        
        function closeDarkness() {
            const overlay = document.getElementById('darkness-overlay');
            const countdownDisplay = document.getElementById('countdown-display');
            const digitalClock = document.getElementById('digital-clock');
            
            // إيقاف المؤقتات
            if (iqamahTimer) {
                clearInterval(iqamahTimer);
                iqamahTimer = null;
            }
            
            if (digitalClockInterval) {
                clearInterval(digitalClockInterval);
                digitalClockInterval = null;
            }
            
            // إخفاء الشاشة
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.style.display = 'none';
                countdownDisplay.style.display = 'block';
                digitalClock.style.display = 'none';
            }, 1000);
            
            log('تم إغلاق شاشة التعتيم');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة الاختبار بنجاح');
        });
    </script>
</body>
</html>
