import React from 'react';
import { TextField } from '@mui/material';
import { BLACKOUT_DURATION } from '../constants/timeConstants';

const BlackoutSettings = () => {
  const blackoutDuration = BLACKOUT_DURATION; // Assuming BLACKOUT_DURATION is a constant value

  return (
    <div>
      <TextField
        label="مدة التعتيم"
        value={blackoutDuration}
        type="number"
        InputProps={{
          readOnly: true,
          disabled: true
        }}
        helperText="لا يمكن تعديل مدة التعتيم يدوياً"
      />
    </div>
  );
};

export default BlackoutSettings;