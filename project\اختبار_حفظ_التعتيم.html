<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حفظ مدة التعتيم</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4CAF50; }
        .error { background: #ffebee; border-color: #f44336; }
        .info { background: #e3f2fd; border-color: #2196F3; }
        .warning { background: #fff3e0; border-color: #FF9800; }
        button { padding: 10px 20px; margin: 5px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-btn { background: #FF9800; }
        .danger-btn { background: #f44336; }
        input[type="number"] { width: 80px; padding: 5px; margin: 0 10px; border: 1px solid #ddd; border-radius: 4px; }
        .log { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; white-space: pre-wrap; }
        .prayer-input { margin: 10px 0; display: flex; align-items: center; }
        .prayer-input label { width: 100px; display: inline-block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار حفظ مدة التعتيم</h1>
        
        <div class="section info">
            <h3>📋 محاكاة حقول الإعدادات</h3>
            <p>هذه نسخة مطابقة لحقول مدة التعتيم في الإعدادات:</p>
            
            <div class="prayer-input">
                <label for="fajr-darkness">الفجر:</label>
                <input type="number" id="fajr-darkness" min="0" max="60" value="0">
                <span>دقيقة</span>
            </div>
            
            <div class="prayer-input">
                <label for="dhuhr-darkness">الظهر:</label>
                <input type="number" id="dhuhr-darkness" min="0" max="60" value="0">
                <span>دقيقة</span>
            </div>
            
            <div class="prayer-input">
                <label for="asr-darkness">العصر:</label>
                <input type="number" id="asr-darkness" min="0" max="60" value="0">
                <span>دقيقة</span>
            </div>
            
            <div class="prayer-input">
                <label for="maghrib-darkness">المغرب:</label>
                <input type="number" id="maghrib-darkness" min="0" max="60" value="0">
                <span>دقيقة</span>
            </div>
            
            <div class="prayer-input">
                <label for="isha-darkness">العشاء:</label>
                <input type="number" id="isha-darkness" min="0" max="60" value="0">
                <span>دقيقة</span>
            </div>
            
            <button id="save-darkness-times">حفظ مدة التعتيم</button>
            <button onclick="loadDarknessTimes()" class="test-btn">تحميل الإعدادات</button>
            <button onclick="clearSettings()" class="danger-btn">مسح الإعدادات</button>
        </div>

        <div class="section">
            <h3>🔍 فحص الحالة الحالية</h3>
            <button onclick="checkCurrentState()">فحص الحالة</button>
            <button onclick="testAutoSave()">اختبار الحفظ التلقائي</button>
            <div id="state-display" class="log"></div>
        </div>

        <div class="section">
            <h3>📊 سجل الأحداث</h3>
            <button onclick="clearLog()">مسح السجل</button>
            <div id="event-log" class="log"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStateDisplay(content) {
            document.getElementById('state-display').textContent = content;
        }

        // نسخة مطابقة لدالة الحفظ في التطبيق الأصلي
        function saveDarknessTimes() {
            log('🔄 حفظ مدة التعتيم...');

            const darknessTimes = {
                fajr: parseInt(document.getElementById('fajr-darkness').value) || 0,
                dhuhr: parseInt(document.getElementById('dhuhr-darkness').value) || 0,
                asr: parseInt(document.getElementById('asr-darkness').value) || 0,
                maghrib: parseInt(document.getElementById('maghrib-darkness').value) || 0,
                isha: parseInt(document.getElementById('isha-darkness').value) || 0
            };

            log(`💾 القيم المحفوظة: ${JSON.stringify(darknessTimes)}`);

            // حفظ في localStorage
            localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
            
            // تحديث المتغير العالمي
            window.darknessTimes = darknessTimes;

            // عرض رسالة تفصيلية
            const message = `تم حفظ مدة التعتيم بنجاح:\n` +
                          `الفجر: ${darknessTimes.fajr} دقيقة\n` +
                          `الظهر: ${darknessTimes.dhuhr} دقيقة\n` +
                          `العصر: ${darknessTimes.asr} دقيقة\n` +
                          `المغرب: ${darknessTimes.maghrib} دقيقة\n` +
                          `العشاء: ${darknessTimes.isha} دقيقة`;
            
            alert(message);
            log('✅ تم حفظ مدة التعتيم بنجاح', 'success');
            
            // تحديث العرض
            checkCurrentState();
        }

        // نسخة مطابقة لدالة التحميل في التطبيق الأصلي
        function loadDarknessTimes() {
            log('📂 تحميل مدة التعتيم المحفوظة...');

            const darknessTimes = localStorage.getItem('darknessTimes');
            if (darknessTimes) {
                try {
                    const times = JSON.parse(darknessTimes);
                    log(`📋 القيم المحفوظة: ${JSON.stringify(times)}`);

                    Object.keys(times).forEach(prayer => {
                        const input = document.getElementById(`${prayer}-darkness`);
                        if (input) {
                            input.value = times[prayer];
                            log(`✅ تم تحميل ${prayer}: ${times[prayer]} دقيقة`, 'success');
                        } else {
                            log(`❌ لم يتم العثور على حقل ${prayer}-darkness`, 'error');
                        }
                    });

                    // تحديث المتغير العالمي
                    window.darknessTimes = times;
                    log('✅ تم تحديث المتغير العالمي', 'success');
                } catch (error) {
                    log(`❌ خطأ في تحليل إعدادات التعتيم: ${error.message}`, 'error');
                }
            } else {
                log('⚠️ لا توجد قيم محفوظة لمدة التعتيم', 'warning');
                // تعيين قيم افتراضية
                const defaultTimes = {
                    fajr: 10, dhuhr: 10, asr: 10, maghrib: 10, isha: 10
                };
                window.darknessTimes = defaultTimes;
                
                // تحديث الحقول بالقيم الافتراضية
                Object.keys(defaultTimes).forEach(prayer => {
                    const input = document.getElementById(`${prayer}-darkness`);
                    if (input && input.value === '0') {
                        input.value = defaultTimes[prayer];
                    }
                });
                log('✅ تم تعيين القيم الافتراضية', 'success');
            }
            
            checkCurrentState();
        }

        function checkCurrentState() {
            let stateInfo = 'الحالة الحالية:\n\n';
            
            // فحص localStorage
            const savedData = localStorage.getItem('darknessTimes');
            if (savedData) {
                try {
                    const parsed = JSON.parse(savedData);
                    stateInfo += `📁 localStorage:\n${JSON.stringify(parsed, null, 2)}\n\n`;
                } catch (e) {
                    stateInfo += `❌ خطأ في localStorage: ${e.message}\n\n`;
                }
            } else {
                stateInfo += `⚠️ لا توجد بيانات في localStorage\n\n`;
            }
            
            // فحص المتغير العالمي
            if (window.darknessTimes) {
                stateInfo += `🌐 المتغير العالمي:\n${JSON.stringify(window.darknessTimes, null, 2)}\n\n`;
            } else {
                stateInfo += `⚠️ المتغير العالمي غير موجود\n\n`;
            }
            
            // فحص قيم الحقول
            stateInfo += `📝 قيم الحقول الحالية:\n`;
            const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
            prayers.forEach(prayer => {
                const input = document.getElementById(`${prayer}-darkness`);
                if (input) {
                    stateInfo += `${prayer}: ${input.value}\n`;
                }
            });
            
            updateStateDisplay(stateInfo);
        }

        function testAutoSave() {
            log('🧪 اختبار الحفظ التلقائي...');
            
            // تغيير قيمة المغرب كمثال
            const maghribInput = document.getElementById('maghrib-darkness');
            const randomValue = Math.floor(Math.random() * 30) + 5; // قيمة عشوائية بين 5-35
            
            log(`🔄 تغيير قيمة المغرب إلى ${randomValue} دقيقة`);
            maghribInput.value = randomValue;
            
            // محاكاة حدث التغيير
            maghribInput.dispatchEvent(new Event('change'));
            
            setTimeout(() => {
                checkCurrentState();
                log('✅ تم اختبار الحفظ التلقائي', 'success');
            }, 500);
        }

        function clearSettings() {
            if (confirm('هل أنت متأكد من مسح جميع إعدادات التعتيم؟')) {
                localStorage.removeItem('darknessTimes');
                window.darknessTimes = undefined;
                
                // إعادة تعيين الحقول
                const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
                prayers.forEach(prayer => {
                    const input = document.getElementById(`${prayer}-darkness`);
                    if (input) {
                        input.value = 0;
                    }
                });
                
                log('🗑️ تم مسح جميع الإعدادات', 'warning');
                checkCurrentState();
            }
        }

        function clearLog() {
            document.getElementById('event-log').textContent = '';
        }

        // إضافة مستمعات الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 تم تحميل صفحة الاختبار');
            
            // إضافة مستمع لزر الحفظ
            document.getElementById('save-darkness-times').addEventListener('click', saveDarknessTimes);
            
            // إضافة مستمعات للحفظ التلقائي
            const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
            prayers.forEach(prayer => {
                const input = document.getElementById(`${prayer}-darkness`);
                if (input) {
                    input.addEventListener('change', function() {
                        log(`🔄 تغيير مدة التعتيم لصلاة ${prayer} إلى ${this.value} دقيقة`);
                        
                        // تأثير بصري
                        this.style.backgroundColor = '#4CAF50';
                        this.style.color = 'white';
                        setTimeout(() => {
                            this.style.backgroundColor = '';
                            this.style.color = '';
                        }, 1000);
                        
                        // حفظ تلقائي
                        saveDarknessTimes();
                        
                        log(`✅ تم حفظ مدة التعتيم لصلاة ${prayer} تلقائياً`, 'success');
                    });
                }
            });
            
            // تحميل الإعدادات الحالية
            loadDarknessTimes();
        });
    </script>
</body>
</html>
