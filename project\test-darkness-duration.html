<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مدة التعتيم - محدث</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .test-button:hover {
            background-color: #45a049;
        }
        
        .settings-group {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .settings-group h3 {
            margin-top: 0;
            color: #333;
        }
        
        .setting-item {
            margin: 10px 0;
        }
        
        .setting-item label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        
        .setting-item input {
            width: 60px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        #darkness-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 1s ease;
        }
        
        #digital-clock {
            color: white;
            font-size: 20vw;
            font-weight: bold;
            text-align: center;
            direction: ltr;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
        }
        
        .close-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 24px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .info-display {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار مدة التعتيم</h1>
        
        <div class="settings-group">
            <h3>إعدادات التعتيم</h3>
            <div class="setting-item">
                <label>مدة التعتيم:</label>
                <input type="number" id="darkness-duration" value="2" min="1" max="30"> دقيقة
            </div>
            <div class="setting-item">
                <label>اختبار سريع:</label>
                <input type="number" id="quick-test-seconds" value="10" min="5" max="60"> ثانية
            </div>
        </div>
        
        <div class="settings-group">
            <h3>الاختبارات</h3>
            <button class="test-button" onclick="testDarkness()">اختبار التعتيم (بالدقائق)</button>
            <button class="test-button" onclick="testQuickDarkness()">اختبار سريع (بالثواني)</button>
            <button class="test-button" onclick="saveDuration()">حفظ المدة</button>
            <button class="test-button" onclick="loadDuration()">تحميل المدة المحفوظة</button>
        </div>
        
        <div id="log" style="background: #f9f9f9; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
    </div>
    
    <!-- شاشة التعتيم -->
    <div id="darkness-overlay">
        <button class="close-button" onclick="closeDarkness()">×</button>
        <div class="info-display" id="info-display"></div>
        <div id="digital-clock"></div>
    </div>
    
    <script>
        let digitalClockInterval = null;
        let darknessTimer = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function testDarkness() {
            const duration = parseInt(document.getElementById('darkness-duration').value) || 2;
            log(`بدء اختبار التعتيم لمدة ${duration} دقيقة...`);
            startDarkness(duration * 60); // تحويل إلى ثواني
        }
        
        function testQuickDarkness() {
            const duration = parseInt(document.getElementById('quick-test-seconds').value) || 10;
            log(`بدء اختبار سريع للتعتيم لمدة ${duration} ثانية...`);
            startDarkness(duration);
        }
        
        function startDarkness(durationInSeconds) {
            const overlay = document.getElementById('darkness-overlay');
            const digitalClock = document.getElementById('digital-clock');
            const infoDisplay = document.getElementById('info-display');
            
            // إظهار شاشة التعتيم
            overlay.style.display = 'flex';
            overlay.style.opacity = '1';
            
            // عرض معلومات المدة
            const minutes = Math.floor(durationInSeconds / 60);
            const seconds = durationInSeconds % 60;
            let timeLeft = durationInSeconds;
            
            const updateInfo = () => {
                const minsLeft = Math.floor(timeLeft / 60);
                const secsLeft = timeLeft % 60;
                infoDisplay.innerHTML = `
                    المدة الإجمالية: ${minutes}:${String(seconds).padStart(2, '0')}<br>
                    المتبقي: ${minsLeft}:${String(secsLeft).padStart(2, '0')}
                `;
                timeLeft--;
                
                if (timeLeft < 0) {
                    clearInterval(darknessTimer);
                    log('انتهت مدة التعتيم');
                    closeDarkness();
                }
            };
            
            // تحديث الساعة الرقمية
            const updateClock = () => {
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                digitalClock.textContent = `${hours}:${minutes}:${seconds}`;
            };
            
            // بدء التحديثات
            updateInfo();
            updateClock();
            
            digitalClockInterval = setInterval(updateClock, 1000);
            darknessTimer = setInterval(updateInfo, 1000);
            
            log(`بدأ التعتيم لمدة ${durationInSeconds} ثانية`);
        }
        
        function closeDarkness() {
            const overlay = document.getElementById('darkness-overlay');
            
            // إيقاف المؤقتات
            if (digitalClockInterval) {
                clearInterval(digitalClockInterval);
                digitalClockInterval = null;
            }
            
            if (darknessTimer) {
                clearInterval(darknessTimer);
                darknessTimer = null;
            }
            
            // إخفاء الشاشة
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 1000);
            
            log('تم إغلاق شاشة التعتيم');
        }
        
        function saveDuration() {
            const duration = parseInt(document.getElementById('darkness-duration').value) || 10;
            
            // حفظ في التخزين المحلي
            const darknessTimes = {
                fajr: duration,
                dhuhr: duration,
                asr: duration,
                maghrib: duration,
                isha: duration
            };
            
            localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
            localStorage.setItem('darkness_durations', JSON.stringify(darknessTimes));
            
            log(`تم حفظ مدة التعتيم: ${duration} دقيقة لجميع الصلوات`);
        }
        
        function loadDuration() {
            try {
                const saved = localStorage.getItem('darknessTimes');
                if (saved) {
                    const darknessTimes = JSON.parse(saved);
                    const duration = darknessTimes.fajr || 10;
                    document.getElementById('darkness-duration').value = duration;
                    log(`تم تحميل مدة التعتيم المحفوظة: ${duration} دقيقة`);
                } else {
                    log('لا توجد مدة محفوظة');
                }
            } catch (error) {
                log('خطأ في تحميل المدة المحفوظة: ' + error.message);
            }
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة اختبار مدة التعتيم');
            loadDuration(); // تحميل المدة المحفوظة تلقائياً
        });
    </script>
</body>
</html>
