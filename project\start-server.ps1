# سكريبت لتشغيل سيرفر محلي بسيط
$port = 8000
$url = "http://localhost:$port"

Write-Host "🚀 بدء تشغيل السيرفر المحلي..." -ForegroundColor Green
Write-Host "📂 المجلد: $(Get-Location)" -ForegroundColor Yellow
Write-Host "🌐 الرابط: $url" -ForegroundColor Cyan
Write-Host "⏹️  للإيقاف: اضغط Ctrl+C" -ForegroundColor Red
Write-Host ""

# محاولة تشغيل السيرفر باستخدام Python
try {
    python -m http.server $port
} catch {
    Write-Host "❌ Python غير متوفر، محاولة استخدام PowerShell..." -ForegroundColor Yellow
    
    # استخدام PowerShell كسيرفر بسيط
    $listener = New-Object System.Net.HttpListener
    $listener.Prefixes.Add("$url/")
    $listener.Start()
    
    Write-Host "✅ السيرفر يعمل على: $url" -ForegroundColor Green
    Write-Host "📄 افتح الرابط في المتصفح لعرض التطبيق" -ForegroundColor Cyan
    
    # فتح المتصفح تلقائياً
    Start-Process $url
    
    while ($listener.IsListening) {
        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response
        
        $localPath = $request.Url.LocalPath
        if ($localPath -eq "/") {
            $localPath = "/index.html"
        }
        
        $filePath = Join-Path (Get-Location) $localPath.TrimStart('/')
        
        if (Test-Path $filePath) {
            $content = Get-Content $filePath -Raw -Encoding UTF8
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($content)
            
            # تحديد نوع المحتوى
            $extension = [System.IO.Path]::GetExtension($filePath)
            switch ($extension) {
                ".html" { $response.ContentType = "text/html; charset=utf-8" }
                ".css" { $response.ContentType = "text/css" }
                ".js" { $response.ContentType = "application/javascript" }
                ".png" { $response.ContentType = "image/png" }
                ".jpg" { $response.ContentType = "image/jpeg" }
                ".jpeg" { $response.ContentType = "image/jpeg" }
                ".gif" { $response.ContentType = "image/gif" }
                ".mp3" { $response.ContentType = "audio/mpeg" }
                ".wav" { $response.ContentType = "audio/wav" }
                default { $response.ContentType = "application/octet-stream" }
            }
            
            $response.ContentLength64 = $buffer.Length
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
        } else {
            $response.StatusCode = 404
            $notFound = "404 - File Not Found: $localPath"
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($notFound)
            $response.ContentLength64 = $buffer.Length
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
        }
        
        $response.Close()
    }
}
