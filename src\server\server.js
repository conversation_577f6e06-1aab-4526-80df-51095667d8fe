const express = require('express');
const cors = require('cors');
const app = express();
const port = 3000;

app.use(cors());
app.use(express.json());

// تخزين مؤقت للإعدادات
let settings = {
  blackoutDuration: 30
};

// الحصول على إعدادات التعتيم
app.get('/api/settings', (req, res) => {
  res.json(settings);
});

// تحديث إعدادات التعتيم
app.post('/api/settings', (req, res) => {
  const { blackoutDuration } = req.body;
  settings.blackoutDuration = blackoutDuration;
  res.json({ success: true, settings });
});

app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
});
