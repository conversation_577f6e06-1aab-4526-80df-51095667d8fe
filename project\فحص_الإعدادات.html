<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص إعدادات التعتيم</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>فحص إعدادات التعتيم المحفوظة</h1>
    
    <button onclick="checkSettings()">فحص الإعدادات</button>
    <button onclick="clearSettings()">مسح الإعدادات</button>
    <button onclick="setTestSettings()">تعيين إعدادات تجريبية</button>
    
    <div id="results"></div>

    <script>
        function checkSettings() {
            const results = document.getElementById('results');
            results.innerHTML = '<h2>نتائج الفحص:</h2>';
            
            // فحص إعدادات التعتيم
            const darknessTimes = localStorage.getItem('darknessTimes');
            if (darknessTimes) {
                try {
                    const times = JSON.parse(darknessTimes);
                    results.innerHTML += `<div class="result success">
                        <h3>✅ إعدادات التعتيم موجودة:</h3>
                        <pre>${JSON.stringify(times, null, 2)}</pre>
                    </div>`;
                } catch (e) {
                    results.innerHTML += `<div class="result error">
                        <h3>❌ خطأ في قراءة إعدادات التعتيم:</h3>
                        <p>${e.message}</p>
                    </div>`;
                }
            } else {
                results.innerHTML += `<div class="result error">
                    <h3>❌ لا توجد إعدادات تعتيم محفوظة</h3>
                </div>`;
            }
            
            // فحص الإعدادات البديلة
            const darknessDurations = localStorage.getItem('darkness_durations');
            if (darknessDurations) {
                try {
                    const durations = JSON.parse(darknessDurations);
                    results.innerHTML += `<div class="result">
                        <h3>📋 إعدادات تعتيم بديلة موجودة:</h3>
                        <pre>${JSON.stringify(durations, null, 2)}</pre>
                    </div>`;
                } catch (e) {
                    results.innerHTML += `<div class="result error">
                        <h3>❌ خطأ في قراءة الإعدادات البديلة:</h3>
                        <p>${e.message}</p>
                    </div>`;
                }
            }
            
            // فحص المتغيرات العالمية
            if (window.darknessTimes) {
                results.innerHTML += `<div class="result success">
                    <h3>✅ المتغير العالمي موجود:</h3>
                    <pre>${JSON.stringify(window.darknessTimes, null, 2)}</pre>
                </div>`;
            } else {
                results.innerHTML += `<div class="result error">
                    <h3>❌ المتغير العالمي غير موجود</h3>
                </div>`;
            }
            
            // فحص وجود الحقول
            const fields = ['fajr-darkness', 'dhuhr-darkness', 'asr-darkness', 'maghrib-darkness', 'isha-darkness'];
            let fieldsFound = 0;
            fields.forEach(field => {
                if (document.getElementById(field)) {
                    fieldsFound++;
                }
            });
            
            if (fieldsFound > 0) {
                results.innerHTML += `<div class="result success">
                    <h3>✅ تم العثور على ${fieldsFound} من ${fields.length} حقول</h3>
                </div>`;
            } else {
                results.innerHTML += `<div class="result error">
                    <h3>❌ لم يتم العثور على حقول التعتيم</h3>
                    <p>هذا طبيعي إذا كنت في صفحة منفصلة</p>
                </div>`;
            }
        }
        
        function clearSettings() {
            localStorage.removeItem('darknessTimes');
            localStorage.removeItem('darkness_durations');
            alert('تم مسح جميع إعدادات التعتيم');
            checkSettings();
        }
        
        function setTestSettings() {
            const testSettings = {
                fajr: 5,
                dhuhr: 5,
                asr: 5,
                maghrib: 5,
                isha: 5
            };
            localStorage.setItem('darknessTimes', JSON.stringify(testSettings));
            window.darknessTimes = testSettings;
            alert('تم تعيين إعدادات تجريبية (5 دقائق لكل صلاة)');
            checkSettings();
        }
        
        // فحص تلقائي عند تحميل الصفحة
        window.onload = checkSettings;
    </script>
</body>
</html>
