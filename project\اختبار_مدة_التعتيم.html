<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مدة التعتيم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="number"] {
            width: 100px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .test-button {
            background-color: #FF9800;
        }
        .test-button:hover {
            background-color: #F57C00;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار مدة التعتيم</h1>
        <p>هذه صفحة اختبار لوظائف مدة التعتيم الجديدة</p>

        <div class="form-group">
            <label for="fajr-darkness">مدة التعتيم للفجر (دقائق):</label>
            <input type="number" id="fajr-darkness" min="0" max="60" value="10">
        </div>

        <div class="form-group">
            <label for="dhuhr-darkness">مدة التعتيم للظهر (دقائق):</label>
            <input type="number" id="dhuhr-darkness" min="0" max="60" value="10">
        </div>

        <div class="form-group">
            <label for="asr-darkness">مدة التعتيم للعصر (دقائق):</label>
            <input type="number" id="asr-darkness" min="0" max="60" value="10">
        </div>

        <div class="form-group">
            <label for="maghrib-darkness">مدة التعتيم للمغرب (دقائق):</label>
            <input type="number" id="maghrib-darkness" min="0" max="60" value="10">
        </div>

        <div class="form-group">
            <label for="isha-darkness">مدة التعتيم للعشاء (دقائق):</label>
            <input type="number" id="isha-darkness" min="0" max="60" value="10">
        </div>

        <button id="save-darkness-times">حفظ مدة التعتيم</button>
        <button id="load-darkness-times">تحميل مدة التعتيم</button>
        <button id="test-darkness-times" class="test-button">اختبار مدة التعتيم</button>

        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        // متغير عالمي لتخزين مدة التعتيم
        let darknessTimes = {
            fajr: 10,
            dhuhr: 10,
            asr: 10,
            maghrib: 10,
            isha: 10
        };

        // دالة لعرض الرسائل
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // دالة لحفظ مدة التعتيم
        function saveDarknessTimes() {
            try {
                console.log('🔄 حفظ مدة التعتيم...');

                const darknessTimes = {
                    fajr: parseInt(document.getElementById('fajr-darkness').value) || 0,
                    dhuhr: parseInt(document.getElementById('dhuhr-darkness').value) || 0,
                    asr: parseInt(document.getElementById('asr-darkness').value) || 0,
                    maghrib: parseInt(document.getElementById('maghrib-darkness').value) || 0,
                    isha: parseInt(document.getElementById('isha-darkness').value) || 0
                };

                console.log('💾 القيم المحفوظة:', darknessTimes);

                // حفظ في localStorage
                localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
                window.darknessTimes = darknessTimes;

                // عرض رسالة تفصيلية
                const message = `تم حفظ مدة التعتيم بنجاح:\n` +
                              `الفجر: ${darknessTimes.fajr} دقيقة\n` +
                              `الظهر: ${darknessTimes.dhuhr} دقيقة\n` +
                              `العصر: ${darknessTimes.asr} دقيقة\n` +
                              `المغرب: ${darknessTimes.maghrib} دقيقة\n` +
                              `العشاء: ${darknessTimes.isha} دقيقة`;
                
                showStatus('تم حفظ مدة التعتيم بنجاح', 'success');
                console.log('✅ تم حفظ مدة التعتيم بنجاح');
                
                return true;
            } catch (error) {
                console.error('خطأ في حفظ مدة التعتيم:', error);
                showStatus('حدث خطأ في حفظ مدة التعتيم', 'error');
                return false;
            }
        }

        // دالة لتحميل مدة التعتيم
        function loadDarknessTimes() {
            try {
                console.log('📂 تحميل مدة التعتيم المحفوظة...');

                const savedTimes = localStorage.getItem('darknessTimes');
                if (savedTimes) {
                    const times = JSON.parse(savedTimes);
                    console.log('📋 القيم المحفوظة:', times);

                    // تحديث الحقول
                    document.getElementById('fajr-darkness').value = times.fajr || 10;
                    document.getElementById('dhuhr-darkness').value = times.dhuhr || 10;
                    document.getElementById('asr-darkness').value = times.asr || 10;
                    document.getElementById('maghrib-darkness').value = times.maghrib || 10;
                    document.getElementById('isha-darkness').value = times.isha || 10;

                    // تحديث المتغير العالمي
                    window.darknessTimes = times;
                    
                    showStatus('تم تحميل مدة التعتيم بنجاح', 'success');
                    console.log('✅ تم تحميل مدة التعتيم بنجاح');
                } else {
                    showStatus('لا توجد قيم محفوظة لمدة التعتيم', 'info');
                    console.log('⚠️ لا توجد قيم محفوظة لمدة التعتيم');
                }
            } catch (error) {
                console.error('خطأ في تحميل مدة التعتيم:', error);
                showStatus('حدث خطأ في تحميل مدة التعتيم', 'error');
            }
        }

        // دالة لاختبار مدة التعتيم
        function testDarknessTimes() {
            try {
                console.log('🧪 اختبار مدة التعتيم...');
                
                // الحصول على القيم الحالية
                const currentTimes = {
                    fajr: parseInt(document.getElementById('fajr-darkness').value) || 0,
                    dhuhr: parseInt(document.getElementById('dhuhr-darkness').value) || 0,
                    asr: parseInt(document.getElementById('asr-darkness').value) || 0,
                    maghrib: parseInt(document.getElementById('maghrib-darkness').value) || 0,
                    isha: parseInt(document.getElementById('isha-darkness').value) || 0
                };
                
                // اختيار صلاة للاختبار (العصر كمثال)
                const testPrayer = 'asr';
                const testDuration = currentTimes[testPrayer];
                
                if (testDuration <= 0) {
                    showStatus('يرجى تعيين مدة تعتيم أكبر من 0 للعصر لإجراء الاختبار', 'error');
                    return;
                }
                
                // عرض رسالة تأكيد
                if (confirm(`هل تريد اختبار التعتيم لمدة ${testDuration} دقيقة؟\n(سيتم عرض رسالة اختبار)`)) {
                    // محاكاة الاختبار
                    showStatus(`تم بدء اختبار التعتيم لمدة ${testDuration} دقيقة`, 'info');
                    console.log(`🎬 بدء اختبار التعتيم لمدة ${testDuration} دقيقة`);
                    
                    // محاكاة انتهاء الاختبار بعد 3 ثوان
                    setTimeout(() => {
                        showStatus('انتهى اختبار التعتيم بنجاح', 'success');
                        console.log('✅ انتهى اختبار التعتيم بنجاح');
                    }, 3000);
                }
            } catch (error) {
                console.error('خطأ في اختبار مدة التعتيم:', error);
                showStatus('حدث خطأ في اختبار مدة التعتيم', 'error');
            }
        }

        // إضافة مستمعات الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل القيم المحفوظة عند تحميل الصفحة
            loadDarknessTimes();

            // إضافة مستمعات الأحداث للأزرار
            document.getElementById('save-darkness-times').addEventListener('click', saveDarknessTimes);
            document.getElementById('load-darkness-times').addEventListener('click', loadDarknessTimes);
            document.getElementById('test-darkness-times').addEventListener('click', testDarknessTimes);

            // إضافة الحفظ التلقائي عند تغيير القيم
            const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
            prayers.forEach(prayer => {
                const input = document.getElementById(`${prayer}-darkness`);
                if (input) {
                    input.addEventListener('change', function() {
                        console.log(`🔄 تغيير مدة التعتيم لصلاة ${prayer} إلى ${this.value} دقيقة`);
                        
                        // تأثير بصري
                        this.style.backgroundColor = '#4CAF50';
                        this.style.color = 'white';
                        setTimeout(() => {
                            this.style.backgroundColor = '';
                            this.style.color = '';
                        }, 1000);
                        
                        // حفظ تلقائي
                        saveDarknessTimes();
                        
                        console.log(`✅ تم حفظ مدة التعتيم لصلاة ${prayer} تلقائياً`);
                    });
                }
            });
        });
    </script>
</body>
</html>
