<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأذان الفوري</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        
        .setting {
            margin: 15px 0;
            text-align: right;
        }
        
        .setting label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        .setting input {
            width: 80px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
            font-size: 16px;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px 5px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
            width: 100%;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-button.urgent {
            background: linear-gradient(45deg, #FF5722, #E64A19);
            font-size: 18px;
            padding: 20px 40px;
        }
        
        .test-button.main-app {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        #log {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
            border: 1px solid #ddd;
            text-align: right;
        }
        
        .current-time {
            font-size: 24px;
            font-weight: bold;
            color: #2196F3;
            margin: 20px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 اختبار الأذان الفوري</h1>
        
        <div class="current-time" id="current-time"></div>
        
        <div class="status error">
            <strong>⚠️ مشكلة الأذان:</strong><br>
            لم ينطلق الأذان ولم يبدأ التعتيم عند وقت العصر
        </div>
        
        <div class="test-section">
            <h3>🚨 اختبار فوري</h3>
            
            <div class="setting">
                <label>مدة الإقامة:</label>
                <input type="number" id="asr-iqama-duration" value="1" min="1" max="40"> دقيقة
            </div>
            
            <div class="setting">
                <label>مدة التعتيم:</label>
                <input type="number" id="asr-darkness" value="2" min="0" max="60"> دقيقة
            </div>
            
            <button class="test-button urgent" onclick="forceAdhanNow()">🔔 تشغيل الأذان الآن!</button>
        </div>
        
        <div class="test-section">
            <h3>🔍 تشخيص المشكلة</h3>
            <button class="test-button" onclick="checkPrayerTimes()">📋 فحص أوقات الصلاة</button>
            <button class="test-button" onclick="checkAdhanSettings()">⚙️ فحص إعدادات الأذان</button>
            <button class="test-button" onclick="simulateAsrTime()">🕐 محاكاة وقت العصر</button>
        </div>
        
        <div class="test-section">
            <h3>🌐 التطبيق الرئيسي</h3>
            <button class="test-button main-app" onclick="openMainApp()">🌐 فتح التطبيق الرئيسي</button>
        </div>
        
        <div class="status info">
            <strong>📋 خطوات التشخيص:</strong><br>
            1. فحص أوقات الصلاة المحفوظة<br>
            2. فحص إعدادات الأذان<br>
            3. اختبار تشغيل الأذان فور<|im_start|><br>
            4. التأكد من عمل التعتيم
        </div>
        
        <div id="log"></div>
    </div>
    
    <!-- عناصر الصوت -->
    <audio id="adhan-audio" src="audio/short_azan.mp3"></audio>
    <audio id="iqama-audio" src="audio/short_iqama.mp3"></audio>
    
    <!-- تضمين ملف العد التنازلي -->
    <script src="iqama-countdown.js"></script>
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            document.getElementById('current-time').textContent = `الوقت الحالي: ${timeString}`;
        }
        
        function forceAdhanNow() {
            log('🔔 تشغيل الأذان فور<|im_start|>...');
            
            // قراءة الإعدادات
            const iqamaMinutes = parseInt(document.getElementById('asr-iqama-duration').value) || 1;
            const darknessMinutes = parseInt(document.getElementById('asr-darkness').value) || 2;
            
            log(`⚙️ الإعدادات: إقامة ${iqamaMinutes} دقيقة، تعتيم ${darknessMinutes} دقيقة`);
            
            // تحديث المتغيرات العالمية
            window.iqamahTimes = { asr: iqamaMinutes };
            window.darknessTimes = { asr: darknessMinutes };
            
            // حفظ في التخزين المحلي
            localStorage.setItem('iqama_durations', JSON.stringify(window.iqamahTimes));
            localStorage.setItem('darknessTimes', JSON.stringify(window.darknessTimes));
            
            // تشغيل الأذان
            const adhanAudio = document.getElementById('adhan-audio');
            if (adhanAudio) {
                adhanAudio.currentTime = 0;
                adhanAudio.volume = 1;
                adhanAudio.play()
                    .then(() => {
                        log('✅ بدأ تشغيل الأذان');
                    })
                    .catch(err => {
                        log(`❌ خطأ في تشغيل الأذان: ${err.message}`);
                    });
            }
            
            // بدء العد التنازلي للإقامة
            try {
                startFullScreenIqamaCountdown('asr', 'العصر');
                log('✅ بدأ العد التنازلي للإقامة');
            } catch (error) {
                log(`❌ خطأ في بدء العد التنازلي: ${error.message}`);
            }
        }
        
        function checkPrayerTimes() {
            log('📋 فحص أوقات الصلاة...');
            
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            log(`🌍 المدينة المختارة: ${currentCity}`);
            
            const times = window.prayerTimes?.[currentCity];
            if (times) {
                log(`✅ أوقات الصلاة موجودة:`);
                log(`   - الفجر: ${times.fajr}`);
                log(`   - الظهر: ${times.dhuhr}`);
                log(`   - العصر: ${times.asr}`);
                log(`   - المغرب: ${times.maghrib}`);
                log(`   - العشاء: ${times.isha}`);
            } else {
                log(`❌ أوقات الصلاة غير موجودة للمدينة ${currentCity}`);
            }
            
            // فحص التخزين المحلي
            const savedTimes = localStorage.getItem('prayerTimes');
            if (savedTimes) {
                try {
                    const parsed = JSON.parse(savedTimes);
                    log(`💾 أوقات محفوظة في التخزين المحلي: ${Object.keys(parsed).length} مدينة`);
                } catch (error) {
                    log(`❌ خطأ في قراءة الأوقات المحفوظة: ${error.message}`);
                }
            } else {
                log(`⚠️ لا توجد أوقات محفوظة في التخزين المحلي`);
            }
        }
        
        function checkAdhanSettings() {
            log('⚙️ فحص إعدادات الأذان...');
            
            // فحص إعدادات الأذان
            const adhanSettings = localStorage.getItem('adhanSettings');
            if (adhanSettings) {
                try {
                    const parsed = JSON.parse(adhanSettings);
                    log(`✅ إعدادات الأذان: مفعل=${parsed.enabled}, الصوت=${parsed.sound}`);
                } catch (error) {
                    log(`❌ خطأ في قراءة إعدادات الأذان: ${error.message}`);
                }
            } else {
                log(`⚠️ لا توجد إعدادات أذان محفوظة`);
            }
            
            // فحص عنصر الأذان
            const enableAdhanCheckbox = document.getElementById('enable-adhan');
            if (enableAdhanCheckbox) {
                log(`📋 حالة مربع الأذان: ${enableAdhanCheckbox.checked ? 'مفعل' : 'غير مفعل'}`);
            } else {
                log(`❌ مربع تفعيل الأذان غير موجود`);
            }
            
            // فحص ملفات الصوت
            const adhanAudio = document.getElementById('adhan-audio');
            if (adhanAudio) {
                log(`🔊 عنصر صوت الأذان موجود: ${adhanAudio.src}`);
            } else {
                log(`❌ عنصر صوت الأذان غير موجود`);
            }
        }
        
        function simulateAsrTime() {
            log('🕐 محاكاة وقت العصر...');
            
            const now = new Date();
            const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
            
            log(`⏰ الوقت الحالي: ${currentTime}`);
            
            // محاكاة دخول وقت العصر
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            const times = window.prayerTimes?.[currentCity];
            
            if (times && times.asr) {
                log(`📋 وقت العصر المحفوظ: ${times.asr}`);
                
                if (currentTime === times.asr) {
                    log(`✅ الوقت الحالي يطابق وقت العصر!`);
                    forceAdhanNow();
                } else {
                    log(`⚠️ الوقت الحالي لا يطابق وقت العصر`);
                    log(`💡 لاختبار فوري، انقر "تشغيل الأذان الآن!"`);
                }
            } else {
                log(`❌ وقت العصر غير محفوظ`);
            }
        }
        
        function openMainApp() {
            window.open('index.html', '_blank');
            log('🌐 تم فتح التطبيق الرئيسي في نافذة جديدة');
        }
        
        // تحديث الوقت كل ثانية
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ تم تحميل صفحة اختبار الأذان الفوري');
            log('🔍 هذه الصفحة لتشخيص مشكلة عدم انطلاق الأذان');
            log('💡 استخدم الأزرار أعلاه لفحص المشكلة واختبار الحلول');
            
            // فحص فوري للإعدادات
            setTimeout(() => {
                checkPrayerTimes();
                checkAdhanSettings();
            }, 1000);
        });
    </script>
</body>
</html>
