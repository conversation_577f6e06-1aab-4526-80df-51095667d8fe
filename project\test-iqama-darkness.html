<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإقامة والتعتيم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .test-button:hover {
            background-color: #45a049;
        }
        
        .settings-group {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .settings-group h3 {
            margin-top: 0;
            color: #333;
        }
        
        .setting-item {
            margin: 10px 0;
        }
        
        .setting-item label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        .setting-item input {
            width: 80px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        #darkness-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 1s ease;
        }
        
        #countdown-display {
            color: white;
            font-size: 10vw;
            font-weight: bold;
            text-align: center;
            direction: rtl;
            font-family: Arial, sans-serif;
        }
        
        #digital-clock {
            color: white;
            font-size: 20vw;
            font-weight: bold;
            text-align: center;
            direction: ltr;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
        }
        
        .close-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 24px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        #log {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار الإقامة والتعتيم</h1>
        
        <div class="settings-group">
            <h3>إعدادات الإقامة</h3>
            <div class="setting-item">
                <label>الفجر:</label>
                <input type="number" id="fajr-iqama-duration" value="2" min="1" max="10"> دقيقة
            </div>
            <div class="setting-item">
                <label>الظهر:</label>
                <input type="number" id="dhuhr-iqama-duration" value="2" min="1" max="10"> دقيقة
            </div>
            <div class="setting-item">
                <label>العصر:</label>
                <input type="number" id="asr-iqama-duration" value="2" min="1" max="10"> دقيقة
            </div>
            <div class="setting-item">
                <label>المغرب:</label>
                <input type="number" id="maghrib-iqama-duration" value="2" min="1" max="10"> دقيقة
            </div>
            <div class="setting-item">
                <label>العشاء:</label>
                <input type="number" id="isha-iqama-duration" value="2" min="1" max="10"> دقيقة
            </div>
        </div>
        
        <div class="settings-group">
            <h3>إعدادات التعتيم</h3>
            <div class="setting-item">
                <label>الفجر:</label>
                <input type="number" id="fajr-darkness-duration" value="3" min="1" max="30"> دقيقة
            </div>
            <div class="setting-item">
                <label>الظهر:</label>
                <input type="number" id="dhuhr-darkness-duration" value="3" min="1" max="30"> دقيقة
            </div>
            <div class="setting-item">
                <label>العصر:</label>
                <input type="number" id="asr-darkness-duration" value="3" min="1" max="30"> دقيقة
            </div>
            <div class="setting-item">
                <label>المغرب:</label>
                <input type="number" id="maghrib-darkness-duration" value="3" min="1" max="30"> دقيقة
            </div>
            <div class="setting-item">
                <label>العشاء:</label>
                <input type="number" id="isha-darkness-duration" value="3" min="1" max="30"> دقيقة
            </div>
        </div>
        
        <div class="settings-group">
            <h3>الاختبارات</h3>
            <button class="test-button" onclick="testIqamaCountdown('fajr', 'الفجر')">اختبار إقامة الفجر</button>
            <button class="test-button" onclick="testIqamaCountdown('asr', 'العصر')">اختبار إقامة العصر</button>
            <button class="test-button" onclick="testDarkness('asr')">اختبار التعتيم فقط</button>
            <button class="test-button" onclick="saveSettings()">حفظ الإعدادات</button>
            <button class="test-button" onclick="loadSettings()">تحميل الإعدادات</button>
        </div>
        
        <div id="log"></div>
    </div>
    
    <!-- شاشة التعتيم -->
    <div id="darkness-overlay">
        <button class="close-button" onclick="closeDarkness()">×</button>
        <div id="countdown-display"></div>
        <div id="digital-clock" style="display: none;"></div>
    </div>
    
    <!-- ملفات الصوت -->
    <audio id="iqama-audio" src="audio/short_iqama.mp3"></audio>
    
    <script>
        let iqamahTimer = null;
        let digitalClockInterval = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function testIqamaCountdown(prayerName, arabicName) {
            log(`🔍 بدء اختبار العد التنازلي للإقامة لصلاة ${arabicName} (${prayerName})`);
            
            // الحصول على مدة الإقامة من الحقل
            const inputElement = document.getElementById(`${prayerName}-iqama-duration`);
            let iqamahMinutes = 2; // افتراضي
            
            if (inputElement) {
                const inputValue = parseInt(inputElement.value);
                if (!isNaN(inputValue) && inputValue > 0) {
                    iqamahMinutes = inputValue;
                    log(`✅ تم الحصول على مدة الإقامة من الحقل: ${iqamahMinutes} دقيقة`);
                } else {
                    log(`❌ قيمة الحقل غير صالحة: ${inputElement.value}, استخدام القيمة الافتراضية`);
                }
            } else {
                log(`❌ لم يتم العثور على حقل الإدخال: ${prayerName}-iqama-duration`);
            }
            
            let secondsLeft = iqamahMinutes * 60;
            log(`⏱️ بدء العد التنازلي: ${iqamahMinutes} دقيقة (${secondsLeft} ثانية)`);
            
            // إظهار شاشة التعتيم
            const overlay = document.getElementById('darkness-overlay');
            const countdownDisplay = document.getElementById('countdown-display');
            const digitalClock = document.getElementById('digital-clock');
            
            overlay.style.display = 'flex';
            overlay.style.opacity = '1';
            countdownDisplay.style.display = 'block';
            digitalClock.style.display = 'none';
            
            // تحديث العد التنازلي
            const updateCountdown = () => {
                const minutes = Math.floor(secondsLeft / 60);
                const seconds = secondsLeft % 60;
                
                countdownDisplay.innerHTML = `
                    <div style="margin-bottom: 20px; font-size: 5vw;">صلاة ${arabicName}</div>
                    <div style="font-size: 15vw; margin-bottom: 20px;">
                        ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}
                    </div>
                    <div style="font-size: 4vw;">الوقت المتبقي للإقامة</div>
                `;
                
                secondsLeft--;
                
                if (secondsLeft < 0) {
                    clearInterval(iqamahTimer);
                    log('⏰ انتهى العد التنازلي للإقامة');
                    
                    // تشغيل صوت الإقامة
                    const iqamaAudio = document.getElementById('iqama-audio');
                    if (iqamaAudio) {
                        iqamaAudio.play().then(() => {
                            log('🔊 تم تشغيل صوت الإقامة');
                        }).catch(err => {
                            log('❌ خطأ في تشغيل صوت الإقامة: ' + err.message);
                        });
                    }
                    
                    // بدء التعتيم
                    setTimeout(() => {
                        testDarkness(prayerName);
                    }, 2000);
                }
            };
            
            // بدء العد التنازلي
            iqamahTimer = setInterval(updateCountdown, 1000);
            updateCountdown(); // تحديث فوري
        }
        
        function testDarkness(prayerName) {
            log(`🌙 بدء اختبار التعتيم لصلاة ${prayerName}`);
            
            // الحصول على مدة التعتيم من الحقل
            const darknessDurationInput = document.getElementById(`${prayerName}-darkness-duration`);
            let darknessMinutes = 3; // افتراضي
            
            if (darknessDurationInput) {
                const inputValue = parseInt(darknessDurationInput.value);
                if (!isNaN(inputValue) && inputValue > 0) {
                    darknessMinutes = inputValue;
                    log(`✅ تم الحصول على مدة التعتيم من الحقل: ${darknessMinutes} دقيقة`);
                } else {
                    log(`❌ قيمة حقل التعتيم غير صالحة: ${darknessDurationInput.value}, استخدام القيمة الافتراضية`);
                }
            } else {
                log(`❌ لم يتم العثور على حقل التعتيم: ${prayerName}-darkness-duration`);
            }
            
            const overlay = document.getElementById('darkness-overlay');
            const countdownDisplay = document.getElementById('countdown-display');
            const digitalClock = document.getElementById('digital-clock');
            
            // إخفاء العد التنازلي وإظهار الساعة الرقمية
            countdownDisplay.style.display = 'none';
            digitalClock.style.display = 'block';
            
            // تحديث الساعة الرقمية
            const updateClock = () => {
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                digitalClock.textContent = `${hours}:${minutes}:${seconds}`;
            };
            
            // بدء تحديث الساعة
            digitalClockInterval = setInterval(updateClock, 1000);
            updateClock(); // تحديث فوري
            
            log(`⏱️ بدء التعتيم لمدة ${darknessMinutes} دقيقة`);
            
            // إخفاء التعتيم بعد المدة المحددة
            setTimeout(() => {
                closeDarkness();
                log('🌅 انتهى التعتيم');
            }, darknessMinutes * 60 * 1000);
        }
        
        function closeDarkness() {
            const overlay = document.getElementById('darkness-overlay');
            const countdownDisplay = document.getElementById('countdown-display');
            const digitalClock = document.getElementById('digital-clock');
            
            // إيقاف المؤقتات
            if (iqamahTimer) {
                clearInterval(iqamahTimer);
                iqamahTimer = null;
            }
            
            if (digitalClockInterval) {
                clearInterval(digitalClockInterval);
                digitalClockInterval = null;
            }
            
            // إخفاء الشاشة
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.style.display = 'none';
                countdownDisplay.style.display = 'block';
                digitalClock.style.display = 'none';
            }, 1000);
            
            log('❌ تم إغلاق شاشة التعتيم يدوياً');
        }
        
        function saveSettings() {
            const iqamaTimes = {
                fajr: parseInt(document.getElementById('fajr-iqama-duration').value) || 2,
                dhuhr: parseInt(document.getElementById('dhuhr-iqama-duration').value) || 2,
                asr: parseInt(document.getElementById('asr-iqama-duration').value) || 2,
                maghrib: parseInt(document.getElementById('maghrib-iqama-duration').value) || 2,
                isha: parseInt(document.getElementById('isha-iqama-duration').value) || 2
            };
            
            const darknessTimes = {
                fajr: parseInt(document.getElementById('fajr-darkness-duration').value) || 3,
                dhuhr: parseInt(document.getElementById('dhuhr-darkness-duration').value) || 3,
                asr: parseInt(document.getElementById('asr-darkness-duration').value) || 3,
                maghrib: parseInt(document.getElementById('maghrib-darkness-duration').value) || 3,
                isha: parseInt(document.getElementById('isha-darkness-duration').value) || 3
            };
            
            localStorage.setItem('iqama_durations', JSON.stringify(iqamaTimes));
            localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
            
            log('💾 تم حفظ الإعدادات');
            log(`📝 مدة الإقامة: ${JSON.stringify(iqamaTimes)}`);
            log(`🌙 مدة التعتيم: ${JSON.stringify(darknessTimes)}`);
        }
        
        function loadSettings() {
            try {
                const savedIqama = JSON.parse(localStorage.getItem('iqama_durations') || '{}');
                const savedDarkness = JSON.parse(localStorage.getItem('darknessTimes') || '{}');
                
                // تحديث حقول الإقامة
                if (savedIqama.fajr) document.getElementById('fajr-iqama-duration').value = savedIqama.fajr;
                if (savedIqama.dhuhr) document.getElementById('dhuhr-iqama-duration').value = savedIqama.dhuhr;
                if (savedIqama.asr) document.getElementById('asr-iqama-duration').value = savedIqama.asr;
                if (savedIqama.maghrib) document.getElementById('maghrib-iqama-duration').value = savedIqama.maghrib;
                if (savedIqama.isha) document.getElementById('isha-iqama-duration').value = savedIqama.isha;
                
                // تحديث حقول التعتيم
                if (savedDarkness.fajr) document.getElementById('fajr-darkness-duration').value = savedDarkness.fajr;
                if (savedDarkness.dhuhr) document.getElementById('dhuhr-darkness-duration').value = savedDarkness.dhuhr;
                if (savedDarkness.asr) document.getElementById('asr-darkness-duration').value = savedDarkness.asr;
                if (savedDarkness.maghrib) document.getElementById('maghrib-darkness-duration').value = savedDarkness.maghrib;
                if (savedDarkness.isha) document.getElementById('isha-darkness-duration').value = savedDarkness.isha;
                
                log('📂 تم تحميل الإعدادات المحفوظة');
                log(`📝 مدة الإقامة: ${JSON.stringify(savedIqama)}`);
                log(`🌙 مدة التعتيم: ${JSON.stringify(savedDarkness)}`);
            } catch (error) {
                log('❌ خطأ في تحميل الإعدادات: ' + error.message);
            }
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل صفحة اختبار الإقامة والتعتيم');
            loadSettings(); // تحميل الإعدادات المحفوظة تلقائياً
        });
    </script>
</body>
</html>
