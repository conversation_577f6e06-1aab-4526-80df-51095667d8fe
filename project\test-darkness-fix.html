<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح مدة التعتيم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
            text-align: right;
        }
        
        .test-section h3 {
            color: #555;
            margin-top: 0;
            text-align: center;
        }
        
        .setting {
            margin: 15px 0;
        }
        
        .setting label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        .setting input {
            width: 80px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
            font-size: 16px;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px 5px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
            width: 100%;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-button.main-app {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        #log {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
            border: 1px solid #ddd;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح مدة التعتيم</h1>
        
        <div class="status success">
            <strong>✅ تم إصلاح المشكلة!</strong><br>
            الآن يقرأ النظام مدة التعتيم من الحقول المدخلة يدو<|im_start|>
        </div>
        
        <div class="test-section">
            <h3>⚙️ إعدادات مدة التعتيم</h3>
            
            <div class="setting">
                <label>الفجر:</label>
                <input type="number" id="fajr-darkness" value="5" min="0" max="60"> دقيقة
            </div>
            
            <div class="setting">
                <label>الظهر:</label>
                <input type="number" id="dhuhr-darkness" value="7" min="0" max="60"> دقيقة
            </div>
            
            <div class="setting">
                <label>العصر:</label>
                <input type="number" id="asr-darkness" value="3" min="0" max="60"> دقيقة
            </div>
            
            <div class="setting">
                <label>المغرب:</label>
                <input type="number" id="maghrib-darkness" value="8" min="0" max="60"> دقيقة
            </div>
            
            <div class="setting">
                <label>العشاء:</label>
                <input type="number" id="isha-darkness" value="10" min="0" max="60"> دقيقة
            </div>
            
            <button class="test-button" onclick="saveAndTest()">💾 حفظ واختبار</button>
        </div>
        
        <div class="test-section">
            <h3>🧪 اختبار قراءة القيم</h3>
            <button class="test-button" onclick="testReadValues()">🔍 اختبار قراءة القيم</button>
            <button class="test-button" onclick="simulateMainApp()">🎯 محاكاة التطبيق الرئيسي</button>
        </div>
        
        <div class="test-section">
            <h3>🌐 التطبيق الرئيسي</h3>
            <div class="status info">
                <strong>💡 للاختبار الكامل:</strong><br>
                افتح التطبيق الرئيسي واختبر من الإعدادات
            </div>
            <button class="test-button main-app" onclick="openMainApp()">🌐 فتح التطبيق الرئيسي</button>
        </div>
        
        <div class="status warning">
            <strong>📋 خطوات الاختبار:</strong><br>
            1. اضبط مدة التعتيم للعصر: 3 دقائق<br>
            2. انقر "حفظ واختبار"<br>
            3. افتح التطبيق الرئيسي<br>
            4. اختبر الأذان والإقامة<br>
            5. تأكد من أن التعتيم ينتهي بعد 3 دقائق
        </div>
        
        <div id="log"></div>
    </div>
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function saveAndTest() {
            log('💾 حفظ إعدادات مدة التعتيم...');
            
            const darknessTimes = {
                fajr: parseInt(document.getElementById('fajr-darkness').value) || 0,
                dhuhr: parseInt(document.getElementById('dhuhr-darkness').value) || 0,
                asr: parseInt(document.getElementById('asr-darkness').value) || 0,
                maghrib: parseInt(document.getElementById('maghrib-darkness').value) || 0,
                isha: parseInt(document.getElementById('isha-darkness').value) || 0
            };
            
            log(`📋 القيم المحفوظة: ${JSON.stringify(darknessTimes)}`);
            
            localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
            window.darknessTimes = darknessTimes;
            
            log('✅ تم حفظ الإعدادات بنجاح');
            log('🎯 جاهز للاختبار في التطبيق الرئيسي');
        }
        
        function testReadValues() {
            log('🔍 اختبار قراءة القيم...');
            
            const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
            
            prayers.forEach(prayer => {
                const input = document.getElementById(`${prayer}-darkness`);
                if (input) {
                    const value = input.value;
                    const parsedValue = parseInt(value);
                    
                    log(`📝 ${prayer}: القيمة="${value}", المحولة=${parsedValue}, صالحة=${!isNaN(parsedValue) && parsedValue >= 0}`);
                } else {
                    log(`❌ ${prayer}: الحقل غير موجود`);
                }
            });
        }
        
        function simulateMainApp() {
            log('🎯 محاكاة قراءة القيم كما في التطبيق الرئيسي...');
            
            const prayerName = 'asr';
            let darknessMinutes = 10; // القيمة الافتراضية
            
            log(`🔍 البحث عن مدة التعتيم لصلاة ${prayerName}...`);
            
            // محاولة قراءة من الحقول المباشرة
            const darknessInput = document.getElementById(`${prayerName}-darkness`);
            if (darknessInput && darknessInput.value !== '') {
                const inputValue = parseInt(darknessInput.value);
                if (!isNaN(inputValue) && inputValue >= 0) {
                    darknessMinutes = inputValue;
                    log(`✅ تم الحصول على مدة التعتيم من الحقل: ${darknessMinutes} دقيقة`);
                } else {
                    log(`❌ قيمة الحقل غير صالحة: ${darknessInput.value}`);
                }
            } else if (window.darknessTimes && window.darknessTimes[prayerName]) {
                darknessMinutes = window.darknessTimes[prayerName];
                log(`✅ تم الحصول على مدة التعتيم من المتغير العالمي: ${darknessMinutes} دقيقة`);
            } else {
                log(`⚠️ استخدام مدة التعتيم الافتراضية: ${darknessMinutes} دقيقة`);
            }
            
            log(`🎯 مدة التعتيم النهائية لصلاة ${prayerName}: ${darknessMinutes} دقيقة`);
        }
        
        function openMainApp() {
            window.open('index.html', '_blank');
            log('🌐 تم فتح التطبيق الرئيسي في نافذة جديدة');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ تم تحميل صفحة اختبار إصلاح مدة التعتيم');
            log('🔧 تم إصلاح قراءة مدة التعتيم من الحقول');
            log('💡 اضبط القيم واحفظها ثم اختبر في التطبيق الرئيسي');
            
            // تحميل القيم المحفوظة
            const savedData = localStorage.getItem('darknessTimes');
            if (savedData) {
                const darknessTimes = JSON.parse(savedData);
                Object.keys(darknessTimes).forEach(prayer => {
                    const input = document.getElementById(`${prayer}-darkness`);
                    if (input) {
                        input.value = darknessTimes[prayer];
                    }
                });
                log('📂 تم تحميل القيم المحفوظة');
            }
        });
    </script>
</body>
</html>
